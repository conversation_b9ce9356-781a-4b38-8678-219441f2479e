# Whitespace - SaaS Landing Page

Un site SaaS moderne et responsive construit avec Next.js 14, TypeScript, Tailwind CSS et Framer Motion.

## 🚀 Fonctionnalités

- **Design moderne et responsive** - Optimisé pour tous les appareils
- **Animations fluides** - Utilise Framer Motion pour des animations élégantes
- **Performance optimisée** - Construit avec Next.js 14 et App Router
- **TypeScript** - Code typé pour une meilleure maintenabilité
- **Composants modulaires** - Architecture basée sur des composants réutilisables
- **SEO optimisé** - Métadonnées et structure optimisées pour les moteurs de recherche

## 🛠️ Technologies utilisées

- **Next.js 14** - Framework React avec App Router
- **TypeScript** - Langage de programmation typé
- **Tailwind CSS** - Framework CSS utilitaire
- **Framer Motion** - Bibliothèque d'animations
- **Lucide React** - Icônes modernes
- **Radix UI** - Composants UI accessibles

## 📁 Structure du projet

```
src/
├── app/
│   ├── globals.css
│   ├── layout.tsx
│   └── page.tsx
├── components/
│   ├── Header.tsx
│   ├── Hero.tsx
│   ├── ProjectManagement.tsx
│   ├── WorkTogether.tsx
│   ├── UseAsExtension.tsx
│   ├── Customize.tsx
│   ├── Pricing.tsx
│   ├── MobileSection.tsx
│   ├── DataSecurity.tsx
│   ├── Sponsors.tsx
│   ├── AppIntegrations.tsx
│   ├── Testimonials.tsx
│   ├── FinalCTA.tsx
│   └── Footer.tsx
├── data/
│   ├── pricing.json
│   └── testimonials.json
└── lib/
    └── utils.ts
```

## 🎨 Sections du site

1. **Header** - Navigation sticky avec logo et menu
2. **Hero** - Section d'accueil avec CTA principal
3. **Project Management** - Présentation des fonctionnalités de gestion de projet
4. **Work Together** - Section collaboration avec visualisation des utilisateurs
5. **Use as Extension** - Présentation de l'extension navigateur
6. **Customize** - Options de personnalisation
7. **Pricing** - Plans tarifaires avec 3 options
8. **Mobile Section** - Disponibilité multi-plateforme
9. **Data Security** - Sécurité et confidentialité des données
10. **Sponsors** - Logos des partenaires
11. **App Integrations** - Intégrations avec d'autres applications
12. **Testimonials** - Témoignages clients
13. **Final CTA** - Appel à l'action final
14. **Footer** - Liens et informations de contact

## 🚀 Installation et démarrage

1. **Installer les dépendances**
```bash
npm install
```

2. **Démarrer le serveur de développement**
```bash
npm run dev
```

3. **Ouvrir le navigateur**
Aller à [http://localhost:3000](http://localhost:3000)

## 📱 Responsive Design

Le site est entièrement responsive et optimisé pour :
- **Mobile** - Smartphones (320px+)
- **Tablet** - Tablettes (768px+)
- **Desktop** - Ordinateurs (1024px+)
- **Large screens** - Grands écrans (1280px+)

## 🎯 Optimisations

- **SSG (Static Site Generation)** - Pages générées statiquement
- **Lazy loading** - Chargement différé des composants
- **Optimisation des images** - Utilisation du composant Image de Next.js
- **Bundle splitting** - Division automatique du code
- **CSS optimisé** - Purge automatique des styles inutilisés

## 🔧 Personnalisation

### Couleurs
Les couleurs principales peuvent être modifiées dans `src/app/globals.css` :
```css
:root {
  --primary: #4F46E5;
  --primary-dark: #3730A3;
  --secondary: #F8FAFC;
  --accent: #FFE492;
}
```

### Données
- **Plans pricing** : `src/data/pricing.json`
- **Témoignages** : `src/data/testimonials.json`

### Composants
Chaque section est un composant indépendant dans `src/components/` et peut être facilement modifié ou remplacé.

## 📄 Scripts disponibles

- `npm run dev` - Démarrer le serveur de développement
- `npm run build` - Construire l'application pour la production
- `npm run start` - Démarrer l'application en mode production
- `npm run lint` - Vérifier le code avec ESLint

## 🌟 Fonctionnalités avancées

- **Animations au scroll** - Éléments animés lors du défilement
- **Hover effects** - Effets interactifs au survol
- **Smooth scrolling** - Défilement fluide entre les sections
- **Loading states** - États de chargement pour une meilleure UX
- **Accessibility** - Optimisé pour l'accessibilité (WCAG)

---

Développé avec ❤️ par l'équipe Whitespace
