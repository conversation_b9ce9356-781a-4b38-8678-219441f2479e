# Guide de déploiement - Whitespace SaaS

Ce guide explique comment déployer le site Whitespace SaaS sur différentes plateformes.

## 🚀 Déploiement rapide

### Vercel (Recommandé)

1. **Connecter le repository**
   ```bash
   npm install -g vercel
   vercel
   ```

2. **Configuration automatique**
   - Vercel détecte automatiquement Next.js
   - Le fichier `vercel.json` configure l'export statique
   - Build command: `npm run build`
   - Output directory: `out`

3. **Variables d'environnement**
   - Copier `.env.example` vers `.env.local`
   - Configurer les variables dans le dashboard Vercel

### Netlify

1. **Déploiement via Git**
   - Connecter le repository sur netlify.com
   - Build command: `npm run build`
   - Publish directory: `out`

2. **Configuration**
   - Le fichier `netlify.toml` configure automatiquement:
     - Redirections SPA
     - Headers de sécurité
     - Cache des assets statiques

### GitHub Pages

1. **Configuration du workflow**
   ```yaml
   # .github/workflows/deploy.yml
   name: Deploy to GitHub Pages
   on:
     push:
       branches: [ main ]
   jobs:
     build-and-deploy:
       runs-on: ubuntu-latest
       steps:
         - uses: actions/checkout@v2
         - uses: actions/setup-node@v2
           with:
             node-version: '18'
         - run: npm ci
         - run: npm run build
         - uses: peaceiris/actions-gh-pages@v3
           with:
             github_token: ${{ secrets.GITHUB_TOKEN }}
             publish_dir: ./out
   ```

## 🛠️ Build local

### Développement
```bash
npm run dev
```
Ouvre http://localhost:3000

### Build de production
```bash
npm run build
```
Génère le dossier `out/` avec les fichiers statiques

### Serveur statique local
```bash
npm run serve
```
Ouvre http://localhost:3001 avec les fichiers buildés

## 📁 Structure de déploiement

```
out/
├── index.html          # Page principale
├── 404.html           # Page d'erreur 404
├── _next/             # Assets Next.js
│   ├── static/        # CSS, JS, images
│   └── ...
├── favicon.ico        # Favicon
└── *.svg             # Icônes SVG
```

## 🔧 Configuration avancée

### Variables d'environnement

Créer un fichier `.env.local` :
```bash
NEXT_PUBLIC_SITE_URL=https://votre-domaine.com
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX
```

### Domaine personnalisé

#### Vercel
1. Aller dans Project Settings > Domains
2. Ajouter votre domaine
3. Configurer les DNS selon les instructions

#### Netlify
1. Aller dans Site Settings > Domain management
2. Ajouter un domaine personnalisé
3. Configurer les DNS

### Optimisations

#### Performance
- Les images sont optimisées automatiquement
- CSS et JS sont minifiés
- Gzip/Brotli activé automatiquement

#### SEO
- Métadonnées configurées dans `layout.tsx`
- Sitemap généré automatiquement
- Structure sémantique HTML

#### Sécurité
- Headers de sécurité configurés
- CSP (Content Security Policy) recommandé
- HTTPS forcé en production

## 🚨 Dépannage

### Erreurs de build

1. **Erreur TypeScript**
   ```bash
   npm run lint
   ```

2. **Erreur de dépendances**
   ```bash
   rm -rf node_modules package-lock.json
   npm install
   ```

3. **Erreur d'export statique**
   - Vérifier `next.config.js`
   - S'assurer que `output: 'export'` est configuré

### Problèmes de déploiement

1. **404 sur les routes**
   - Vérifier la configuration des redirections
   - S'assurer que le SPA routing est configuré

2. **Assets manquants**
   - Vérifier le `basePath` dans `next.config.js`
   - S'assurer que les chemins sont relatifs

3. **Variables d'environnement**
   - Préfixer avec `NEXT_PUBLIC_` pour le client
   - Configurer dans le dashboard de la plateforme

## 📊 Monitoring

### Analytics
- Google Analytics (optionnel)
- Vercel Analytics
- Netlify Analytics

### Performance
- Lighthouse CI
- Web Vitals
- Bundle analyzer

### Erreurs
- Sentry (optionnel)
- Logs de la plateforme

## 🔄 CI/CD

### GitHub Actions
```yaml
name: CI/CD
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
      - run: npm ci
      - run: npm run lint
      - run: npm run build
```

### Déploiement automatique
- Push sur `main` → Déploiement automatique
- Pull requests → Preview deployments
- Tags → Releases

---

Pour plus d'informations, consulter la documentation officielle de chaque plateforme.
