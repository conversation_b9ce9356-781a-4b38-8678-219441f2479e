import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "Whitespace - Project Management & Collaboration Tool",
  description: "Get more done with Whitespace. Project management software that enables your teams to collaborate, plan, analyze and manage everyday tasks.",
  keywords: "project management, collaboration, productivity, whitespace, team management",
  authors: [{ name: "Whitespace Team" }],
  openGraph: {
    title: "Whitespace - Project Management & Collaboration Tool",
    description: "Get more done with Whitespace. Project management software that enables your teams to collaborate, plan, analyze and manage everyday tasks.",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "Whitespace - Project Management & Collaboration Tool",
    description: "Get more done with Whitespace. Project management software that enables your teams to collaborate, plan, analyze and manage everyday tasks.",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <body className={`${inter.variable} font-sans antialiased`}>
        {children}
      </body>
    </html>
  );
}
