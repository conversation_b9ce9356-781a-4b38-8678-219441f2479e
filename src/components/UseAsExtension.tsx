'use client'

import { motion } from 'framer-motion'
import { ArrowRight, Download, Chrome, Globe } from 'lucide-react'

const UseAsExtension = () => {
  const browsers = [
    { name: 'Chrome', icon: Chrome, users: '2M+' },
    { name: 'Firefox', icon: Globe, users: '500K+' },
    { name: 'Safari', icon: Globe, users: '300K+' },
  ]

  return (
    <section className="section-padding bg-gray-50">
      <div className="container-custom">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            <div className="space-y-6">
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 leading-tight">
                Use as{' '}
                <span className="text-blue-600">Extension</span>
              </h2>
              <p className="text-lg text-gray-600 leading-relaxed">
                Use the web clipper extension, available on all major browsers, to save web pages
                or take screenshots as notes. Access Whitespace quickly from your browser toolbar.
              </p>
            </div>

            <div className="space-y-4">
              <h3 className="text-xl font-semibold text-gray-900">Available for:</h3>
              <div className="grid sm:grid-cols-3 gap-4">
                {browsers.map((browser, index) => (
                  <motion.div
                    key={browser.name}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="flex items-center space-x-3 p-4 bg-white rounded-lg border border-gray-200 hover:border-blue-300 transition-colors duration-200"
                  >
                    <browser.icon className="w-8 h-8 text-gray-700" />
                    <div>
                      <div className="font-medium text-gray-900">{browser.name}</div>
                      <div className="text-sm text-gray-500">{browser.users} users</div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>

            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="btn-primary flex items-center space-x-2 group"
            >
              <Download className="w-5 h-5" />
              <span>Install Extension</span>
              <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-200" />
            </motion.button>
          </motion.div>

          {/* Right Content - Extension Mockup */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="relative"
          >
            <div className="relative bg-white rounded-2xl shadow-2xl p-6 transform -rotate-3 hover:rotate-0 transition-transform duration-500">
              {/* Browser Window Mockup */}
              <div className="space-y-4">
                {/* Browser Header */}
                <div className="flex items-center justify-between">
                  <div className="flex space-x-2">
                    <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                    <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                    <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                  </div>
                  <div className="text-xs text-gray-400">browser.com</div>
                </div>

                {/* Extension Popup */}
                <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                      <span className="text-white font-bold text-sm">W</span>
                    </div>
                    <div>
                      <div className="font-semibold text-gray-900">Whitespace</div>
                      <div className="text-xs text-gray-500">Web Clipper</div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <button className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg text-sm font-medium">
                      Save Page
                    </button>
                    <button className="w-full bg-gray-200 text-gray-700 py-2 px-4 rounded-lg text-sm font-medium">
                      Take Screenshot
                    </button>
                    <button className="w-full bg-gray-200 text-gray-700 py-2 px-4 rounded-lg text-sm font-medium">
                      Quick Note
                    </button>
                  </div>
                </div>
              </div>

              {/* Floating Elements */}
              <motion.div
                animate={{ y: [-5, 5, -5] }}
                transition={{ duration: 2, repeat: Infinity }}
                className="absolute -top-3 -right-3 w-6 h-6 bg-blue-500 rounded-full shadow-lg"
              ></motion.div>

              <motion.div
                animate={{ y: [5, -5, 5] }}
                transition={{ duration: 3, repeat: Infinity }}
                className="absolute -bottom-3 -left-3 w-4 h-4 bg-yellow-400 rounded-full shadow-lg"
              ></motion.div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default UseAsExtension
