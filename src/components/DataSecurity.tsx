'use client'

import { motion } from 'framer-motion'
import { ArrowRight, Shield, Lock, Eye, Server, Key, CheckCircle } from 'lucide-react'

const DataSecurity = () => {
  const securityFeatures = [
    {
      icon: Shield,
      title: 'End-to-End Encryption',
      description: 'Your data is encrypted before it leaves your device'
    },
    {
      icon: Lock,
      title: 'Zero-Knowledge Architecture',
      description: 'We cannot see your data, even if we wanted to'
    },
    {
      icon: Eye,
      title: 'Privacy First',
      description: 'No tracking, no ads, no data mining'
    },
    {
      icon: Server,
      title: 'Secure Servers',
      description: 'Hosted on enterprise-grade secure infrastructure'
    }
  ]

  return (
    <section className="section-padding bg-white">
      <div className="container-custom">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left Content - Security Visualization */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="relative"
          >
            <div className="relative bg-gradient-to-br from-green-50 to-blue-50 rounded-2xl p-8">
              {/* Security Dashboard Mockup */}
              <div className="bg-white rounded-xl shadow-lg p-6 space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold text-gray-900">Security Status</h3>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                    <span className="text-sm text-green-600 font-medium">Secure</span>
                  </div>
                </div>

                {/* Security Metrics */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-green-50 rounded-lg p-4 text-center">
                    <Shield className="w-8 h-8 text-green-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-green-600">256-bit</div>
                    <div className="text-xs text-green-700">Encryption</div>
                  </div>

                  <div className="bg-blue-50 rounded-lg p-4 text-center">
                    <Lock className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-blue-600">100%</div>
                    <div className="text-xs text-blue-700">Private</div>
                  </div>
                </div>

                {/* Security Checklist */}
                <div className="space-y-3">
                  <div className="text-sm font-medium text-gray-700">Security Features</div>
                  {[
                    'Two-factor authentication',
                    'End-to-end encryption',
                    'Regular security audits',
                    'GDPR compliant'
                  ].map((feature, index) => (
                    <motion.div
                      key={feature}
                      initial={{ opacity: 0, x: -20 }}
                      whileInView={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                      viewport={{ once: true }}
                      className="flex items-center space-x-3"
                    >
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      <span className="text-sm text-gray-600">{feature}</span>
                    </motion.div>
                  ))}
                </div>
              </div>

              {/* Floating Security Elements */}
              <motion.div
                animate={{
                  rotate: [0, 360],
                  scale: [1, 1.1, 1]
                }}
                transition={{
                  duration: 8,
                  repeat: Infinity,
                  ease: "linear"
                }}
                className="absolute -top-4 -right-4 w-12 h-12 bg-green-500 rounded-full shadow-lg flex items-center justify-center"
              >
                <Key className="w-6 h-6 text-white" />
              </motion.div>

              <motion.div
                animate={{
                  y: [-10, 10, -10],
                  rotate: [0, 180, 360]
                }}
                transition={{
                  duration: 6,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                className="absolute -bottom-4 -left-4 w-8 h-8 bg-blue-500 rounded-full shadow-lg flex items-center justify-center"
              >
                <Shield className="w-4 h-4 text-white" />
              </motion.div>
            </div>
          </motion.div>

          {/* Right Content */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            <div className="space-y-6">
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 leading-tight">
                100% your data
              </h2>
              <p className="text-lg text-gray-600 leading-relaxed">
                The app is open source and your notes are saved to an open format, so you&apos;ll always have
                access to them. Uses End-To-End Encryption (E2EE) to secure your notes and ensure no-one
                but yourself can access them.
              </p>
            </div>

            <div className="space-y-6">
              {securityFeatures.map((feature, index) => (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="flex items-start space-x-4"
                >
                  <div className="flex-shrink-0 w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                    <feature.icon className="w-6 h-6 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">{feature.title}</h3>
                    <p className="text-gray-600">{feature.description}</p>
                  </div>
                </motion.div>
              ))}
            </div>

            <div className="bg-green-50 border border-green-200 rounded-lg p-6">
              <div className="flex items-start space-x-3">
                <Shield className="w-6 h-6 text-green-600 mt-1" />
                <div>
                  <h4 className="font-semibold text-green-900 mb-2">Security Guarantee</h4>
                  <p className="text-green-700 text-sm">
                    We&apos;re so confident in our security that we offer a $10,000 bug bounty for anyone
                    who can compromise our encryption.
                  </p>
                </div>
              </div>
            </div>

            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="btn-primary flex items-center space-x-2 group"
            >
              <span>Read our security</span>
              <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-200" />
            </motion.button>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default DataSecurity
