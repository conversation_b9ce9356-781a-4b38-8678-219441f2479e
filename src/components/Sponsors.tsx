'use client'

import { motion } from 'framer-motion'

const Sponsors = () => {
  const sponsors = [
    { name: 'Apple', logo: '🍎' },
    { name: 'Microsoft', logo: '🪟' },
    { name: 'Slack', logo: '💬' },
    { name: 'Google', logo: '🔍' },
  ]

  return (
    <section className="section-padding bg-white">
      <div className="container-custom">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center space-y-12"
        >
          <h2 className="text-2xl md:text-3xl font-bold text-gray-900">
            Our sponsors
          </h2>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 items-center">
            {sponsors.map((sponsor, index) => (
              <motion.div
                key={sponsor.name}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ scale: 1.05 }}
                className="flex flex-col items-center space-y-4 p-6 rounded-xl hover:bg-gray-50 transition-colors duration-200 cursor-pointer"
              >
                <div className="text-4xl">{sponsor.logo}</div>
                <span className="text-lg font-semibold text-gray-700">{sponsor.name}</span>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  )
}

export default Sponsors
