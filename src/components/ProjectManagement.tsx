'use client'

import { motion } from 'framer-motion'
import { ArrowRight, CheckCircle, Users, Calendar, BarChart3 } from 'lucide-react'

const ProjectManagement = () => {
  const features = [
    {
      icon: CheckCircle,
      title: 'Task Management',
      description: 'Organize and track all your tasks in one place'
    },
    {
      icon: Users,
      title: 'Team Collaboration',
      description: 'Work together seamlessly with your team members'
    },
    {
      icon: Calendar,
      title: 'Timeline Planning',
      description: 'Plan and visualize your project timelines'
    },
    {
      icon: BarChart3,
      title: 'Progress Tracking',
      description: 'Monitor project progress with detailed analytics'
    }
  ]

  return (
    <section className="section-padding bg-white">
      <div className="container-custom">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            <div className="space-y-6">
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 leading-tight">
                Project{' '}
                <span className="text-blue-600">Management</span>
              </h2>
              <p className="text-lg text-gray-600 leading-relaxed">
                Images, videos, PDFs and audio files are supported. Create math expressions and 
                diagrams directly from the app. Take photos with the mobile app and save them 
                to a note.
              </p>
            </div>

            <div className="grid sm:grid-cols-2 gap-6">
              {features.map((feature, index) => (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="flex items-start space-x-3"
                >
                  <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                    <feature.icon className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-1">{feature.title}</h3>
                    <p className="text-sm text-gray-600">{feature.description}</p>
                  </div>
                </motion.div>
              ))}
            </div>

            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="btn-primary flex items-center space-x-2 group"
            >
              <span>Get Started</span>
              <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-200" />
            </motion.button>
          </motion.div>

          {/* Right Content - Illustration */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="relative"
          >
            <div className="relative bg-gradient-to-br from-blue-50 to-indigo-100 rounded-2xl p-8">
              {/* Project Board Mockup */}
              <div className="bg-white rounded-xl shadow-lg p-6 space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold text-gray-900">Project Board</h3>
                  <div className="flex space-x-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                    <div className="w-2 h-2 bg-red-400 rounded-full"></div>
                  </div>
                </div>
                
                <div className="grid grid-cols-3 gap-4">
                  {/* To Do Column */}
                  <div className="space-y-3">
                    <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">To Do</div>
                    <div className="space-y-2">
                      <div className="bg-gray-50 rounded-lg p-3 border-l-4 border-gray-300">
                        <div className="h-3 bg-gray-200 rounded w-3/4 mb-2"></div>
                        <div className="h-2 bg-gray-100 rounded w-1/2"></div>
                      </div>
                      <div className="bg-gray-50 rounded-lg p-3 border-l-4 border-gray-300">
                        <div className="h-3 bg-gray-200 rounded w-2/3 mb-2"></div>
                        <div className="h-2 bg-gray-100 rounded w-3/4"></div>
                      </div>
                    </div>
                  </div>
                  
                  {/* In Progress Column */}
                  <div className="space-y-3">
                    <div className="text-xs font-medium text-yellow-600 uppercase tracking-wide">In Progress</div>
                    <div className="space-y-2">
                      <div className="bg-yellow-50 rounded-lg p-3 border-l-4 border-yellow-400">
                        <div className="h-3 bg-yellow-200 rounded w-4/5 mb-2"></div>
                        <div className="h-2 bg-yellow-100 rounded w-2/3"></div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Done Column */}
                  <div className="space-y-3">
                    <div className="text-xs font-medium text-green-600 uppercase tracking-wide">Done</div>
                    <div className="space-y-2">
                      <div className="bg-green-50 rounded-lg p-3 border-l-4 border-green-400">
                        <div className="h-3 bg-green-200 rounded w-3/4 mb-2"></div>
                        <div className="h-2 bg-green-100 rounded w-1/2"></div>
                      </div>
                      <div className="bg-green-50 rounded-lg p-3 border-l-4 border-green-400">
                        <div className="h-3 bg-green-200 rounded w-2/3 mb-2"></div>
                        <div className="h-2 bg-green-100 rounded w-3/4"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Floating Elements */}
              <motion.div
                animate={{ rotate: [0, 360] }}
                transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                className="absolute -top-4 -right-4 w-8 h-8 bg-blue-500 rounded-full shadow-lg flex items-center justify-center"
              >
                <CheckCircle className="w-4 h-4 text-white" />
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default ProjectManagement
