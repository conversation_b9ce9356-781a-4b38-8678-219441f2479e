'use client'

import { motion } from 'framer-motion'
import { ArrowRight, Smartphone, Tablet, Monitor, Wifi } from 'lucide-react'

const MobileSection = () => {
  const devices = [
    { icon: Smartphone, name: 'Mobile', description: 'iOS & Android apps' },
    { icon: Tablet, name: 'Tablet', description: 'Optimized for tablets' },
    { icon: Monitor, name: 'Desktop', description: 'Windows, Mac & Linux' },
  ]

  return (
    <section className="section-padding bg-gradient-to-br from-blue-600 to-indigo-700 text-white relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <motion.div
          animate={{ 
            rotate: [0, 360],
            scale: [1, 1.2, 1]
          }}
          transition={{ 
            duration: 20, 
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute -top-24 -left-24 w-48 h-48 bg-white/10 rounded-full"
        ></motion.div>
        
        <motion.div
          animate={{ 
            rotate: [360, 0],
            scale: [1, 1.1, 1]
          }}
          transition={{ 
            duration: 15, 
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute -bottom-16 -right-16 w-32 h-32 bg-white/10 rounded-full"
        ></motion.div>
      </div>

      <div className="container-custom relative z-10">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left Content */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            <div className="space-y-6">
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold leading-tight">
                Your work, everywhere you are
              </h2>
              <p className="text-lg text-blue-100 leading-relaxed">
                Access your notes from your computer, phone or tablet by synchronising with various services, 
                including whitespace, Dropbox and OneDrive. The app is available on Windows, macOS, Linux, 
                Android and iOS. A terminal app is also available!
              </p>
            </div>

            <div className="space-y-6">
              <h3 className="text-xl font-semibold">Available on all platforms:</h3>
              <div className="grid sm:grid-cols-3 gap-4">
                {devices.map((device, index) => (
                  <motion.div
                    key={device.name}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="flex flex-col items-center space-y-3 p-6 bg-white/10 rounded-xl backdrop-blur-sm hover:bg-white/20 transition-colors duration-200"
                  >
                    <device.icon className="w-8 h-8" />
                    <div className="text-center">
                      <div className="font-medium">{device.name}</div>
                      <div className="text-sm text-blue-200">{device.description}</div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>

            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-white text-blue-600 font-semibold px-8 py-4 rounded-lg hover:bg-gray-50 transition-colors duration-200 flex items-center space-x-2 group"
            >
              <span>Try Taskey</span>
              <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-200" />
            </motion.button>
          </motion.div>

          {/* Right Content - Device Mockups */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="relative"
          >
            <div className="relative">
              {/* Phone Mockup */}
              <motion.div
                animate={{ y: [-10, 10, -10] }}
                transition={{ duration: 4, repeat: Infinity }}
                className="relative z-20 bg-white rounded-3xl p-2 shadow-2xl transform rotate-12 hover:rotate-6 transition-transform duration-500"
                style={{ width: '200px', height: '400px' }}
              >
                <div className="bg-gray-900 rounded-2xl h-full p-4 flex flex-col">
                  {/* Phone Header */}
                  <div className="flex items-center justify-between mb-4">
                    <div className="text-white text-xs">9:41</div>
                    <div className="flex space-x-1">
                      <div className="w-1 h-1 bg-white rounded-full"></div>
                      <div className="w-1 h-1 bg-white rounded-full"></div>
                      <div className="w-1 h-1 bg-white rounded-full"></div>
                    </div>
                  </div>
                  
                  {/* App Content */}
                  <div className="flex-1 space-y-3">
                    <div className="bg-blue-600 rounded-lg p-3">
                      <div className="w-8 h-8 bg-white rounded-lg mb-2 flex items-center justify-center">
                        <span className="text-blue-600 font-bold text-sm">W</span>
                      </div>
                      <div className="text-white text-xs">Whitespace</div>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="h-2 bg-gray-700 rounded w-3/4"></div>
                      <div className="h-2 bg-gray-700 rounded w-1/2"></div>
                      <div className="h-2 bg-gray-700 rounded w-2/3"></div>
                    </div>
                  </div>
                </div>
              </motion.div>

              {/* Tablet Mockup */}
              <motion.div
                animate={{ y: [10, -10, 10] }}
                transition={{ duration: 5, repeat: Infinity }}
                className="absolute top-20 -left-20 z-10 bg-white rounded-2xl p-3 shadow-xl transform -rotate-12 hover:-rotate-6 transition-transform duration-500"
                style={{ width: '280px', height: '200px' }}
              >
                <div className="bg-gray-100 rounded-xl h-full p-4">
                  <div className="flex items-center space-x-2 mb-3">
                    <div className="w-6 h-6 bg-blue-600 rounded-lg flex items-center justify-center">
                      <span className="text-white font-bold text-xs">W</span>
                    </div>
                    <div className="text-gray-900 text-sm font-medium">Whitespace</div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-2">
                    <div className="h-16 bg-blue-100 rounded-lg"></div>
                    <div className="h-16 bg-purple-100 rounded-lg"></div>
                  </div>
                </div>
              </motion.div>

              {/* Sync Icon */}
              <motion.div
                animate={{ rotate: [0, 360] }}
                transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
                className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-30 w-12 h-12 bg-yellow-400 rounded-full shadow-lg flex items-center justify-center"
              >
                <Wifi className="w-6 h-6 text-gray-900" />
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default MobileSection
