'use client'

import { motion } from 'framer-motion'
import { ArrowR<PERSON>, Palette, Layout, Setting<PERSON>, Zap } from 'lucide-react'

const Customize = () => {
  const customizations = [
    {
      icon: Palette,
      title: 'Themes & Colors',
      description: 'Choose from multiple themes and customize colors to match your brand'
    },
    {
      icon: Layout,
      title: 'Layout Options',
      description: 'Arrange your workspace exactly how you want it with flexible layouts'
    },
    {
      icon: Settings,
      title: 'Advanced Settings',
      description: 'Fine-tune every aspect of your workspace with powerful settings'
    },
    {
      icon: Zap,
      title: 'Workflow Automation',
      description: 'Create custom workflows and automate repetitive tasks'
    }
  ]

  return (
    <section className="section-padding bg-white">
      <div className="container-custom">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left Content - Customization Interface */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="relative"
          >
            <div className="relative bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl p-8">
              {/* Customization Panel Mockup */}
              <div className="bg-white rounded-xl shadow-lg p-6 space-y-6">
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold text-gray-900">Customize Workspace</h3>
                  <Settings className="w-5 h-5 text-gray-400" />
                </div>

                {/* Theme Selection */}
                <div className="space-y-3">
                  <div className="text-sm font-medium text-gray-700">Theme</div>
                  <div className="flex space-x-3">
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      className="w-12 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg cursor-pointer border-2 border-blue-500"
                    ></motion.div>
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      className="w-12 h-8 bg-gradient-to-r from-green-400 to-blue-500 rounded-lg cursor-pointer border-2 border-transparent hover:border-gray-300"
                    ></motion.div>
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      className="w-12 h-8 bg-gradient-to-r from-pink-400 to-red-500 rounded-lg cursor-pointer border-2 border-transparent hover:border-gray-300"
                    ></motion.div>
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      className="w-12 h-8 bg-gradient-to-r from-gray-700 to-gray-900 rounded-lg cursor-pointer border-2 border-transparent hover:border-gray-300"
                    ></motion.div>
                  </div>
                </div>

                {/* Layout Options */}
                <div className="space-y-3">
                  <div className="text-sm font-medium text-gray-700">Layout</div>
                  <div className="grid grid-cols-3 gap-2">
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      className="aspect-square bg-blue-100 rounded-lg border-2 border-blue-500 cursor-pointer flex items-center justify-center"
                    >
                      <Layout className="w-4 h-4 text-blue-600" />
                    </motion.div>
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      className="aspect-square bg-gray-100 rounded-lg border-2 border-transparent hover:border-gray-300 cursor-pointer flex items-center justify-center"
                    >
                      <Layout className="w-4 h-4 text-gray-400" />
                    </motion.div>
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      className="aspect-square bg-gray-100 rounded-lg border-2 border-transparent hover:border-gray-300 cursor-pointer flex items-center justify-center"
                    >
                      <Layout className="w-4 h-4 text-gray-400" />
                    </motion.div>
                  </div>
                </div>

                {/* Settings Toggles */}
                <div className="space-y-3">
                  <div className="text-sm font-medium text-gray-700">Features</div>
                  <div className="space-y-2">
                    {['Dark mode', 'Notifications', 'Auto-save'].map((feature, index) => (
                      <div key={feature} className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">{feature}</span>
                        <motion.div
                          whileHover={{ scale: 1.05 }}
                          className={`w-10 h-6 rounded-full cursor-pointer transition-colors duration-200 ${
                            index === 0 ? 'bg-blue-500' : 'bg-gray-300'
                          }`}
                        >
                          <motion.div
                            animate={{ x: index === 0 ? 16 : 2 }}
                            className="w-5 h-5 bg-white rounded-full mt-0.5 shadow-sm"
                          ></motion.div>
                        </motion.div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Floating Elements */}
              <motion.div
                animate={{ rotate: [0, 360] }}
                transition={{ duration: 10, repeat: Infinity, ease: "linear" }}
                className="absolute -top-4 -right-4 w-8 h-8 bg-purple-500 rounded-full shadow-lg flex items-center justify-center"
              >
                <Palette className="w-4 h-4 text-white" />
              </motion.div>
            </div>
          </motion.div>

          {/* Right Content */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            <div className="space-y-6">
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 leading-tight">
                Customise it to{' '}
                <span className="text-blue-600">your needs</span>
              </h2>
              <p className="text-lg text-gray-600 leading-relaxed">
                Customise the app with plugins, custom themes and multiple text editors (Rich Text or Markdown).
                Or create your own scripts and plugins using the Extension API.
              </p>
            </div>

            <div className="space-y-6">
              {customizations.map((item, index) => (
                <motion.div
                  key={item.title}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="flex items-start space-x-4"
                >
                  <div className="flex-shrink-0 w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                    <item.icon className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-2">{item.title}</h3>
                    <p className="text-gray-600">{item.description}</p>
                  </div>
                </motion.div>
              ))}
            </div>

            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="btn-primary flex items-center space-x-2 group"
            >
              <span>Let&apos;s Go</span>
              <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-200" />
            </motion.button>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default Customize
