'use client'

import { motion } from 'framer-motion'
import { ArrowRight, Plus } from 'lucide-react'

const AppIntegrations = () => {
  const apps = [
    { name: 'Google Drive', logo: '📁', color: 'bg-blue-100' },
    { name: 'Dropbox', logo: '📦', color: 'bg-blue-200' },
    { name: 'OneDrive', logo: '☁️', color: 'bg-blue-300' },
    { name: 'Slack', logo: '💬', color: 'bg-purple-100' },
    { name: '<PERSON>rell<PERSON>', logo: '📋', color: 'bg-blue-100' },
    { name: 'Notion', logo: '📝', color: 'bg-gray-100' },
    { name: 'Figma', logo: '🎨', color: 'bg-pink-100' },
    { name: 'GitHub', logo: '🐙', color: 'bg-gray-200' },
  ]

  return (
    <section className="section-padding bg-gray-50">
      <div className="container-custom">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center space-y-12"
        >
          <div className="space-y-6">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900">
              Work with Your Favorite Apps Using{' '}
              <span className="text-blue-600">whitespace</span>
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Whitespace teams up with your favorite software. Integrate with over 1000+ apps 
              with Zapier to have all the tools you need for your project success.
            </p>
          </div>

          {/* Apps Grid */}
          <div className="relative">
            {/* Central Whitespace Logo */}
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-20">
              <motion.div
                animate={{ rotate: [0, 360] }}
                transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                className="w-20 h-20 bg-blue-600 rounded-full shadow-xl flex items-center justify-center"
              >
                <span className="text-white font-bold text-2xl">W</span>
              </motion.div>
            </div>

            {/* Apps arranged in a circle */}
            <div className="relative w-96 h-96 mx-auto">
              {apps.map((app, index) => {
                const angle = (index * 360) / apps.length
                const radius = 140
                const x = Math.cos((angle * Math.PI) / 180) * radius
                const y = Math.sin((angle * Math.PI) / 180) * radius

                return (
                  <motion.div
                    key={app.name}
                    initial={{ opacity: 0, scale: 0 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="absolute"
                    style={{
                      left: `calc(50% + ${x}px)`,
                      top: `calc(50% + ${y}px)`,
                      transform: 'translate(-50%, -50%)'
                    }}
                  >
                    <motion.div
                      whileHover={{ scale: 1.1 }}
                      animate={{ 
                        y: [0, -10, 0],
                        rotate: [0, 5, -5, 0]
                      }}
                      transition={{ 
                        duration: 3 + index * 0.2, 
                        repeat: Infinity,
                        ease: "easeInOut"
                      }}
                      className={`w-16 h-16 ${app.color} rounded-xl shadow-lg flex flex-col items-center justify-center cursor-pointer hover:shadow-xl transition-shadow duration-200`}
                    >
                      <div className="text-2xl mb-1">{app.logo}</div>
                      <div className="text-xs font-medium text-gray-700 text-center leading-tight">
                        {app.name}
                      </div>
                    </motion.div>

                    {/* Connection Line */}
                    <svg 
                      className="absolute top-8 left-8 pointer-events-none"
                      style={{
                        width: '200px',
                        height: '200px',
                        transform: 'translate(-50%, -50%)'
                      }}
                    >
                      <motion.line
                        initial={{ pathLength: 0, opacity: 0 }}
                        whileInView={{ pathLength: 1, opacity: 0.2 }}
                        transition={{ duration: 1, delay: index * 0.1 }}
                        viewport={{ once: true }}
                        x1="0"
                        y1="0"
                        x2={-x * 0.7}
                        y2={-y * 0.7}
                        stroke="#3B82F6"
                        strokeWidth="2"
                        strokeDasharray="5,5"
                      />
                    </svg>
                  </motion.div>
                )
              })}
            </div>

            {/* Plus Icons for more integrations */}
            <motion.div
              animate={{ rotate: [0, 360] }}
              transition={{ duration: 15, repeat: Infinity, ease: "linear" }}
              className="absolute top-4 right-4 w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"
            >
              <Plus className="w-4 h-4 text-gray-600" />
            </motion.div>
            
            <motion.div
              animate={{ rotate: [360, 0] }}
              transition={{ duration: 12, repeat: Infinity, ease: "linear" }}
              className="absolute bottom-4 left-4 w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center"
            >
              <Plus className="w-4 h-4 text-gray-600" />
            </motion.div>
          </div>

          {/* CTA */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.5 }}
            viewport={{ once: true }}
            className="space-y-6"
          >
            <p className="text-gray-600">
              Connect with 1000+ apps through Zapier, or use our API to build custom integrations.
            </p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="btn-primary flex items-center space-x-2 group mx-auto"
            >
              <span>View all integrations</span>
              <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-200" />
            </motion.button>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}

export default AppIntegrations
