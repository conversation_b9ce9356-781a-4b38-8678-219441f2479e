'use client'

import { motion } from 'framer-motion'
import { ArrowRight } from 'lucide-react'

const WorkTogether = () => {
  const collaborators = [
    { id: 1, name: '<PERSON>', color: 'bg-pink-400', position: { top: '20%', left: '30%' } },
    { id: 2, name: '<PERSON>', color: 'bg-blue-400', position: { top: '10%', left: '60%' } },
    { id: 3, name: '<PERSON>', color: 'bg-green-400', position: { top: '40%', left: '80%' } },
    { id: 4, name: '<PERSON>', color: 'bg-purple-400', position: { top: '70%', left: '70%' } },
    { id: 5, name: '<PERSON>', color: 'bg-yellow-400', position: { top: '80%', left: '40%' } },
    { id: 6, name: '<PERSON>', color: 'bg-red-400', position: { top: '60%', left: '10%' } },
    { id: 7, name: '<PERSON>', color: 'bg-indigo-400', position: { top: '30%', left: '5%' } },
  ]

  return (
    <section className="section-padding bg-gray-50">
      <div className="container-custom">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left Content - Collaboration Visualization */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="relative h-96 lg:h-[500px]"
          >
            {/* Central Hub */}
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-24 h-24 bg-white rounded-full shadow-xl flex items-center justify-center border-4 border-blue-200">
              <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-lg">W</span>
              </div>
            </div>

            {/* Collaborator Avatars */}
            {collaborators.map((collaborator, index) => (
              <motion.div
                key={collaborator.id}
                initial={{ opacity: 0, scale: 0 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="absolute"
                style={collaborator.position}
              >
                <motion.div
                  animate={{ 
                    y: [0, -10, 0],
                    rotate: [0, 5, -5, 0]
                  }}
                  transition={{ 
                    duration: 3 + index * 0.5, 
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                  className={`w-12 h-12 ${collaborator.color} rounded-full shadow-lg flex items-center justify-center text-white font-semibold cursor-pointer hover:scale-110 transition-transform duration-200`}
                >
                  {collaborator.name.charAt(0)}
                </motion.div>
                
                {/* Connection Lines */}
                <svg 
                  className="absolute top-6 left-6 pointer-events-none"
                  style={{
                    width: '200px',
                    height: '200px',
                    transform: 'translate(-50%, -50%)'
                  }}
                >
                  <motion.line
                    initial={{ pathLength: 0, opacity: 0 }}
                    whileInView={{ pathLength: 1, opacity: 0.3 }}
                    transition={{ duration: 1, delay: index * 0.2 }}
                    viewport={{ once: true }}
                    x1="0"
                    y1="0"
                    x2="100"
                    y2="100"
                    stroke="#3B82F6"
                    strokeWidth="2"
                    strokeDasharray="5,5"
                  />
                </svg>
              </motion.div>
            ))}

            {/* Floating Elements */}
            <motion.div
              animate={{ 
                rotate: [0, 360],
                scale: [1, 1.1, 1]
              }}
              transition={{ 
                duration: 8, 
                repeat: Infinity,
                ease: "linear"
              }}
              className="absolute top-4 right-4 w-6 h-6 bg-yellow-400 rounded-full shadow-lg"
            ></motion.div>
            
            <motion.div
              animate={{ 
                rotate: [360, 0],
                scale: [1, 1.2, 1]
              }}
              transition={{ 
                duration: 6, 
                repeat: Infinity,
                ease: "linear"
              }}
              className="absolute bottom-4 left-4 w-8 h-8 bg-pink-400 rounded-full shadow-lg"
            ></motion.div>
          </motion.div>

          {/* Right Content */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            <div className="space-y-6">
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 leading-tight">
                Work{' '}
                <span className="text-blue-600">together</span>
              </h2>
              <p className="text-lg text-gray-600 leading-relaxed">
                With whitespace, share your notes with your colleagues and collaborate on them. 
                You can also publish a note to the internet and share the URL with others.
              </p>
            </div>

            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                <span className="text-gray-700">Real-time collaboration</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                <span className="text-gray-700">Share notes instantly</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                <span className="text-gray-700">Publish to the web</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                <span className="text-gray-700">Team permissions</span>
              </div>
            </div>

            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="btn-primary flex items-center space-x-2 group"
            >
              <span>Try it now</span>
              <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-200" />
            </motion.button>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default WorkTogether
