[{"/Users/<USER>/Desktop/site web edara/src/app/layout.tsx": "1", "/Users/<USER>/Desktop/site web edara/src/app/page.tsx": "2", "/Users/<USER>/Desktop/site web edara/src/components/AppIntegrations.tsx": "3", "/Users/<USER>/Desktop/site web edara/src/components/Customize.tsx": "4", "/Users/<USER>/Desktop/site web edara/src/components/DataSecurity.tsx": "5", "/Users/<USER>/Desktop/site web edara/src/components/FinalCTA.tsx": "6", "/Users/<USER>/Desktop/site web edara/src/components/Footer.tsx": "7", "/Users/<USER>/Desktop/site web edara/src/components/Header.tsx": "8", "/Users/<USER>/Desktop/site web edara/src/components/Hero.tsx": "9", "/Users/<USER>/Desktop/site web edara/src/components/MobileSection.tsx": "10", "/Users/<USER>/Desktop/site web edara/src/components/Pricing.tsx": "11", "/Users/<USER>/Desktop/site web edara/src/components/ProjectManagement.tsx": "12", "/Users/<USER>/Desktop/site web edara/src/components/Sponsors.tsx": "13", "/Users/<USER>/Desktop/site web edara/src/components/Testimonials.tsx": "14", "/Users/<USER>/Desktop/site web edara/src/components/UseAsExtension.tsx": "15", "/Users/<USER>/Desktop/site web edara/src/components/WorkTogether.tsx": "16", "/Users/<USER>/Desktop/site web edara/src/lib/utils.ts": "17"}, {"size": 1421, "mtime": 1748631089791, "results": "18", "hashOfConfig": "19"}, {"size": 1138, "mtime": 1748631307554, "results": "20", "hashOfConfig": "19"}, {"size": 6810, "mtime": 1748631285418, "results": "21", "hashOfConfig": "19"}, {"size": 8382, "mtime": 1748631901698, "results": "22", "hashOfConfig": "19"}, {"size": 7769, "mtime": 1748631952531, "results": "23", "hashOfConfig": "19"}, {"size": 5042, "mtime": 1748631000188, "results": "24", "hashOfConfig": "19"}, {"size": 6829, "mtime": 1748630962519, "results": "25", "hashOfConfig": "19"}, {"size": 4007, "mtime": 1748630792875, "results": "26", "hashOfConfig": "19"}, {"size": 4576, "mtime": 1748631844192, "results": "27", "hashOfConfig": "19"}, {"size": 7894, "mtime": 1748631200487, "results": "28", "hashOfConfig": "19"}, {"size": 4959, "mtime": 1748630913545, "results": "29", "hashOfConfig": "19"}, {"size": 7172, "mtime": 1748630856957, "results": "30", "hashOfConfig": "19"}, {"size": 1630, "mtime": 1748630975938, "results": "31", "hashOfConfig": "19"}, {"size": 3874, "mtime": 1748632018208, "results": "32", "hashOfConfig": "19"}, {"size": 5898, "mtime": 1748631413312, "results": "33", "hashOfConfig": "19"}, {"size": 6937, "mtime": 1748630889056, "results": "34", "hashOfConfig": "19"}, {"size": 166, "mtime": 1748630773573, "results": "35", "hashOfConfig": "19"}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "khta1f", {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Desktop/site web edara/src/app/layout.tsx", [], [], "/Users/<USER>/Desktop/site web edara/src/app/page.tsx", [], [], "/Users/<USER>/Desktop/site web edara/src/components/AppIntegrations.tsx", [], [], "/Users/<USER>/Desktop/site web edara/src/components/Customize.tsx", [], [], "/Users/<USER>/Desktop/site web edara/src/components/DataSecurity.tsx", [], [], "/Users/<USER>/Desktop/site web edara/src/components/FinalCTA.tsx", [], [], "/Users/<USER>/Desktop/site web edara/src/components/Footer.tsx", [], [], "/Users/<USER>/Desktop/site web edara/src/components/Header.tsx", [], [], "/Users/<USER>/Desktop/site web edara/src/components/Hero.tsx", [], [], "/Users/<USER>/Desktop/site web edara/src/components/MobileSection.tsx", [], [], "/Users/<USER>/Desktop/site web edara/src/components/Pricing.tsx", [], [], "/Users/<USER>/Desktop/site web edara/src/components/ProjectManagement.tsx", [], [], "/Users/<USER>/Desktop/site web edara/src/components/Sponsors.tsx", [], [], "/Users/<USER>/Desktop/site web edara/src/components/Testimonials.tsx", [], [], "/Users/<USER>/Desktop/site web edara/src/components/UseAsExtension.tsx", [], [], "/Users/<USER>/Desktop/site web edara/src/components/WorkTogether.tsx", [], [], "/Users/<USER>/Desktop/site web edara/src/lib/utils.ts", [], []]