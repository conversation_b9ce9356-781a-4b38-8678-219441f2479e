(()=>{var e={};e.id=492,e.ids=[492],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1814:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>p,pages:()=>m,routeModule:()=>c,tree:()=>d});var n=r(5239),o=r(8088),a=r(8170),s=r.n(a),i=r(893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let d={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"/Users/<USER>/Desktop/site web edara/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,m=[],p={require:r,loadChunk:()=>Promise.resolve()},c=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3376:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},3873:e=>{"use strict";e.exports=require("path")},4398:()=>{},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i,metadata:()=>s});var n=r(7413),o=r(1001),a=r.n(o);r(1135);let s={title:"Whitespace - Project Management & Collaboration Tool",description:"Get more done with Whitespace. Project management software that enables your teams to collaborate, plan, analyze and manage everyday tasks.",keywords:"project management, collaboration, productivity, whitespace, team management",authors:[{name:"Whitespace Team"}],openGraph:{title:"Whitespace - Project Management & Collaboration Tool",description:"Get more done with Whitespace. Project management software that enables your teams to collaborate, plan, analyze and manage everyday tasks.",type:"website",locale:"en_US"},twitter:{card:"summary_large_image",title:"Whitespace - Project Management & Collaboration Tool",description:"Get more done with Whitespace. Project management software that enables your teams to collaborate, plan, analyze and manage everyday tasks."}};function i({children:e}){return(0,n.jsx)("html",{lang:"en",className:"scroll-smooth",children:(0,n.jsx)("body",{className:`${a().variable} font-sans antialiased`,children:e})})}},6752:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},8310:()=>{},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[447,553],()=>r(1814));module.exports=n})();