{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/site%20web%20edara/src/components/Header.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { motion } from 'framer-motion'\nimport { Menu, X, ChevronDown } from 'lucide-react'\nimport Link from 'next/link'\n\nconst Header = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false)\n\n  const navItems = [\n    { name: 'Products', href: '#', hasDropdown: true },\n    { name: 'Solutions', href: '#', hasDropdown: true },\n    { name: 'Resources', href: '#', hasDropdown: true },\n    { name: 'Pricing', href: '#pricing' },\n  ]\n\n  return (\n    <header className=\"fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-sm border-b border-gray-100\">\n      <div className=\"container-custom\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n              <span className=\"text-white font-bold text-lg\">W</span>\n            </div>\n            <span className=\"text-xl font-bold text-gray-900\">whitespace</span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <div key={item.name} className=\"relative group\">\n                <Link\n                  href={item.href}\n                  className=\"flex items-center space-x-1 text-gray-700 hover:text-blue-600 transition-colors duration-200\"\n                >\n                  <span>{item.name}</span>\n                  {item.hasDropdown && (\n                    <ChevronDown className=\"w-4 h-4 group-hover:rotate-180 transition-transform duration-200\" />\n                  )}\n                </Link>\n              </div>\n            ))}\n          </nav>\n\n          {/* Desktop CTA Buttons */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <Link\n              href=\"#\"\n              className=\"text-gray-700 hover:text-blue-600 transition-colors duration-200\"\n            >\n              Login\n            </Link>\n            <Link\n              href=\"#\"\n              className=\"btn-primary\"\n            >\n              Try Whitespace free\n            </Link>\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n            className=\"md:hidden p-2 text-gray-700 hover:text-blue-600 transition-colors duration-200\"\n          >\n            {isMenuOpen ? <X className=\"w-6 h-6\" /> : <Menu className=\"w-6 h-6\" />}\n          </button>\n        </div>\n\n        {/* Mobile Menu */}\n        {isMenuOpen && (\n          <motion.div\n            initial={{ opacity: 0, y: -20 }}\n            animate={{ opacity: 1, y: 0 }}\n            exit={{ opacity: 0, y: -20 }}\n            className=\"md:hidden py-4 border-t border-gray-100\"\n          >\n            <nav className=\"flex flex-col space-y-4\">\n              {navItems.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"text-gray-700 hover:text-blue-600 transition-colors duration-200\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  {item.name}\n                </Link>\n              ))}\n              <div className=\"pt-4 border-t border-gray-100 space-y-4\">\n                <Link\n                  href=\"#\"\n                  className=\"block text-gray-700 hover:text-blue-600 transition-colors duration-200\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  Login\n                </Link>\n                <Link\n                  href=\"#\"\n                  className=\"block btn-primary text-center\"\n                  onClick={() => setIsMenuOpen(false)}\n                >\n                  Try Whitespace free\n                </Link>\n              </div>\n            </nav>\n          </motion.div>\n        )}\n      </div>\n    </header>\n  )\n}\n\nexport default Header\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AALA;;;;;;AAOA,MAAM,SAAS;IACb,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,WAAW;QACf;YAAE,MAAM;YAAY,MAAM;YAAK,aAAa;QAAK;QACjD;YAAE,MAAM;YAAa,MAAM;YAAK,aAAa;QAAK;QAClD;YAAE,MAAM;YAAa,MAAM;YAAK,aAAa;QAAK;QAClD;YAAE,MAAM;YAAW,MAAM;QAAW;KACrC;IAED,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,8OAAC;oCAAK,WAAU;8CAAkC;;;;;;;;;;;;sCAIpD,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC;oCAAoB,WAAU;8CAC7B,cAAA,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,KAAK,IAAI;wCACf,WAAU;;0DAEV,8OAAC;0DAAM,KAAK,IAAI;;;;;;4CACf,KAAK,WAAW,kBACf,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;mCAPnB,KAAK,IAAI;;;;;;;;;;sCAevB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAMH,8OAAC;4BACC,SAAS,IAAM,cAAc,CAAC;4BAC9B,WAAU;sCAET,2BAAa,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAAe,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;gBAK7D,4BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC9B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,MAAM;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC3B,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;4BACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,cAAc;8CAE5B,KAAK,IAAI;mCALL,KAAK,IAAI;;;;;0CAQlB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,cAAc;kDAC9B;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,cAAc;kDAC9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;uCAEe", "debugId": null}}, {"offset": {"line": 297, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/site%20web%20edara/src/components/Hero.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { ArrowRight, Play } from 'lucide-react'\nimport Image from 'next/image'\n\nconst Hero = () => {\n  return (\n    <section className=\"pt-24 pb-16 md:pt-32 md:pb-24 bg-gradient-to-br from-blue-50 to-indigo-100\">\n      <div className=\"container-custom\">\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n          {/* Left Content */}\n          <motion.div\n            initial={{ opacity: 0, x: -50 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            className=\"space-y-8\"\n          >\n            <div className=\"space-y-6\">\n              <h1 className=\"text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight\">\n                Get More Done with{' '}\n                <span className=\"text-blue-600\">whitespace</span>\n              </h1>\n              <p className=\"text-lg md:text-xl text-gray-600 leading-relaxed\">\n                Project management software that enables your teams to collaborate, plan, \n                analyze and manage everyday tasks\n              </p>\n            </div>\n\n            <div className=\"flex flex-col sm:flex-row gap-4\">\n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"btn-primary flex items-center justify-center space-x-2 group\"\n              >\n                <span>Try Whitespace free</span>\n                <ArrowRight className=\"w-5 h-5 group-hover:translate-x-1 transition-transform duration-200\" />\n              </motion.button>\n              \n              <motion.button\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n                className=\"btn-secondary flex items-center justify-center space-x-2 group\"\n              >\n                <Play className=\"w-5 h-5\" />\n                <span>Watch Demo</span>\n              </motion.button>\n            </div>\n          </motion.div>\n\n          {/* Right Content - Illustration */}\n          <motion.div\n            initial={{ opacity: 0, x: 50 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            className=\"relative\"\n          >\n            <div className=\"relative bg-white rounded-2xl shadow-2xl p-8 transform rotate-3 hover:rotate-0 transition-transform duration-500\">\n              {/* Mock Dashboard Interface */}\n              <div className=\"space-y-4\">\n                {/* Header */}\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex space-x-2\">\n                    <div className=\"w-3 h-3 bg-red-400 rounded-full\"></div>\n                    <div className=\"w-3 h-3 bg-yellow-400 rounded-full\"></div>\n                    <div className=\"w-3 h-3 bg-green-400 rounded-full\"></div>\n                  </div>\n                  <div className=\"text-xs text-gray-400\">whitespace.com</div>\n                </div>\n                \n                {/* Content */}\n                <div className=\"space-y-3\">\n                  <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n                  <div className=\"h-4 bg-gray-200 rounded w-1/2\"></div>\n                  <div className=\"h-4 bg-blue-200 rounded w-2/3\"></div>\n                  \n                  <div className=\"grid grid-cols-2 gap-4 mt-6\">\n                    <div className=\"h-20 bg-gradient-to-br from-blue-100 to-blue-200 rounded-lg\"></div>\n                    <div className=\"h-20 bg-gradient-to-br from-purple-100 to-purple-200 rounded-lg\"></div>\n                  </div>\n                  \n                  <div className=\"space-y-2 mt-4\">\n                    <div className=\"h-3 bg-gray-100 rounded w-full\"></div>\n                    <div className=\"h-3 bg-gray-100 rounded w-4/5\"></div>\n                    <div className=\"h-3 bg-gray-100 rounded w-3/5\"></div>\n                  </div>\n                </div>\n              </div>\n              \n              {/* Floating Elements */}\n              <motion.div\n                animate={{ y: [-10, 10, -10] }}\n                transition={{ duration: 3, repeat: Infinity }}\n                className=\"absolute -top-4 -right-4 w-8 h-8 bg-yellow-400 rounded-full shadow-lg\"\n              ></motion.div>\n              \n              <motion.div\n                animate={{ y: [10, -10, 10] }}\n                transition={{ duration: 4, repeat: Infinity }}\n                className=\"absolute -bottom-4 -left-4 w-6 h-6 bg-blue-500 rounded-full shadow-lg\"\n              ></motion.div>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  )\n}\n\nexport default Hero\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAMA,MAAM,OAAO;IACX,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;;4CAAyE;4CAClE;0DACnB,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;kDAElC,8OAAC;wCAAE,WAAU;kDAAmD;;;;;;;;;;;;0CAMlE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;;0DAEV,8OAAC;0DAAK;;;;;;0DACN,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;kDAGxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;wCACxB,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;kCAMZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;;;;;;;8DAEjB,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAIzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;;;;;8DAEf,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;;;;;;;8DAGjB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;8CAMrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,GAAG;4CAAC,CAAC;4CAAI;4CAAI,CAAC;yCAAG;oCAAC;oCAC7B,YAAY;wCAAE,UAAU;wCAAG,QAAQ;oCAAS;oCAC5C,WAAU;;;;;;8CAGZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,GAAG;4CAAC;4CAAI,CAAC;4CAAI;yCAAG;oCAAC;oCAC5B,YAAY;wCAAE,UAAU;wCAAG,QAAQ;oCAAS;oCAC5C,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1B;uCAEe", "debugId": null}}, {"offset": {"line": 666, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/site%20web%20edara/src/components/ProjectManagement.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { ArrowRight, CheckCircle, Users, Calendar, BarChart3 } from 'lucide-react'\n\nconst ProjectManagement = () => {\n  const features = [\n    {\n      icon: CheckCircle,\n      title: 'Task Management',\n      description: 'Organize and track all your tasks in one place'\n    },\n    {\n      icon: Users,\n      title: 'Team Collaboration',\n      description: 'Work together seamlessly with your team members'\n    },\n    {\n      icon: Calendar,\n      title: 'Timeline Planning',\n      description: 'Plan and visualize your project timelines'\n    },\n    {\n      icon: BarChart3,\n      title: 'Progress Tracking',\n      description: 'Monitor project progress with detailed analytics'\n    }\n  ]\n\n  return (\n    <section className=\"section-padding bg-white\">\n      <div className=\"container-custom\">\n        <div className=\"grid lg:grid-cols-2 gap-16 items-center\">\n          {/* Left Content */}\n          <motion.div\n            initial={{ opacity: 0, x: -50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"space-y-8\"\n          >\n            <div className=\"space-y-6\">\n              <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 leading-tight\">\n                Project{' '}\n                <span className=\"text-blue-600\">Management</span>\n              </h2>\n              <p className=\"text-lg text-gray-600 leading-relaxed\">\n                Images, videos, PDFs and audio files are supported. Create math expressions and \n                diagrams directly from the app. Take photos with the mobile app and save them \n                to a note.\n              </p>\n            </div>\n\n            <div className=\"grid sm:grid-cols-2 gap-6\">\n              {features.map((feature, index) => (\n                <motion.div\n                  key={feature.title}\n                  initial={{ opacity: 0, y: 20 }}\n                  whileInView={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.5, delay: index * 0.1 }}\n                  viewport={{ once: true }}\n                  className=\"flex items-start space-x-3\"\n                >\n                  <div className=\"flex-shrink-0 w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center\">\n                    <feature.icon className=\"w-5 h-5 text-blue-600\" />\n                  </div>\n                  <div>\n                    <h3 className=\"font-semibold text-gray-900 mb-1\">{feature.title}</h3>\n                    <p className=\"text-sm text-gray-600\">{feature.description}</p>\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n\n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              className=\"btn-primary flex items-center space-x-2 group\"\n            >\n              <span>Get Started</span>\n              <ArrowRight className=\"w-5 h-5 group-hover:translate-x-1 transition-transform duration-200\" />\n            </motion.button>\n          </motion.div>\n\n          {/* Right Content - Illustration */}\n          <motion.div\n            initial={{ opacity: 0, x: 50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            viewport={{ once: true }}\n            className=\"relative\"\n          >\n            <div className=\"relative bg-gradient-to-br from-blue-50 to-indigo-100 rounded-2xl p-8\">\n              {/* Project Board Mockup */}\n              <div className=\"bg-white rounded-xl shadow-lg p-6 space-y-4\">\n                <div className=\"flex items-center justify-between\">\n                  <h3 className=\"font-semibold text-gray-900\">Project Board</h3>\n                  <div className=\"flex space-x-2\">\n                    <div className=\"w-2 h-2 bg-green-400 rounded-full\"></div>\n                    <div className=\"w-2 h-2 bg-yellow-400 rounded-full\"></div>\n                    <div className=\"w-2 h-2 bg-red-400 rounded-full\"></div>\n                  </div>\n                </div>\n                \n                <div className=\"grid grid-cols-3 gap-4\">\n                  {/* To Do Column */}\n                  <div className=\"space-y-3\">\n                    <div className=\"text-xs font-medium text-gray-500 uppercase tracking-wide\">To Do</div>\n                    <div className=\"space-y-2\">\n                      <div className=\"bg-gray-50 rounded-lg p-3 border-l-4 border-gray-300\">\n                        <div className=\"h-3 bg-gray-200 rounded w-3/4 mb-2\"></div>\n                        <div className=\"h-2 bg-gray-100 rounded w-1/2\"></div>\n                      </div>\n                      <div className=\"bg-gray-50 rounded-lg p-3 border-l-4 border-gray-300\">\n                        <div className=\"h-3 bg-gray-200 rounded w-2/3 mb-2\"></div>\n                        <div className=\"h-2 bg-gray-100 rounded w-3/4\"></div>\n                      </div>\n                    </div>\n                  </div>\n                  \n                  {/* In Progress Column */}\n                  <div className=\"space-y-3\">\n                    <div className=\"text-xs font-medium text-yellow-600 uppercase tracking-wide\">In Progress</div>\n                    <div className=\"space-y-2\">\n                      <div className=\"bg-yellow-50 rounded-lg p-3 border-l-4 border-yellow-400\">\n                        <div className=\"h-3 bg-yellow-200 rounded w-4/5 mb-2\"></div>\n                        <div className=\"h-2 bg-yellow-100 rounded w-2/3\"></div>\n                      </div>\n                    </div>\n                  </div>\n                  \n                  {/* Done Column */}\n                  <div className=\"space-y-3\">\n                    <div className=\"text-xs font-medium text-green-600 uppercase tracking-wide\">Done</div>\n                    <div className=\"space-y-2\">\n                      <div className=\"bg-green-50 rounded-lg p-3 border-l-4 border-green-400\">\n                        <div className=\"h-3 bg-green-200 rounded w-3/4 mb-2\"></div>\n                        <div className=\"h-2 bg-green-100 rounded w-1/2\"></div>\n                      </div>\n                      <div className=\"bg-green-50 rounded-lg p-3 border-l-4 border-green-400\">\n                        <div className=\"h-3 bg-green-200 rounded w-2/3 mb-2\"></div>\n                        <div className=\"h-2 bg-green-100 rounded w-3/4\"></div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n              \n              {/* Floating Elements */}\n              <motion.div\n                animate={{ rotate: [0, 360] }}\n                transition={{ duration: 20, repeat: Infinity, ease: \"linear\" }}\n                className=\"absolute -top-4 -right-4 w-8 h-8 bg-blue-500 rounded-full shadow-lg flex items-center justify-center\"\n              >\n                <CheckCircle className=\"w-4 h-4 text-white\" />\n              </motion.div>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  )\n}\n\nexport default ProjectManagement\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,oBAAoB;IACxB,MAAM,WAAW;QACf;YACE,MAAM,2NAAA,CAAA,cAAW;YACjB,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,0MAAA,CAAA,WAAQ;YACd,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,kNAAA,CAAA,YAAS;YACf,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;;4CAAyE;4CAC7E;0DACR,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;kDAElC,8OAAC;wCAAE,WAAU;kDAAwC;;;;;;;;;;;;0CAOvD,8OAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO,QAAQ;wCAAI;wCAChD,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,QAAQ,IAAI;oDAAC,WAAU;;;;;;;;;;;0DAE1B,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAoC,QAAQ,KAAK;;;;;;kEAC/D,8OAAC;wDAAE,WAAU;kEAAyB,QAAQ,WAAW;;;;;;;;;;;;;uCAZtD,QAAQ,KAAK;;;;;;;;;;0CAkBxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAU;;kDAEV,8OAAC;kDAAK;;;;;;kDACN,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;kCAK1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA8B;;;;;;8DAC5C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;;;;;;;;;;;;;sDAInB,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAA4D;;;;;;sEAC3E,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;;;;;sFACf,8OAAC;4EAAI,WAAU;;;;;;;;;;;;8EAEjB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;;;;;sFACf,8OAAC;4EAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;8DAMrB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAA8D;;;;;;sEAC7E,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;;;;;kFACf,8OAAC;wEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;8DAMrB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAA6D;;;;;;sEAC5E,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;;;;;sFACf,8OAAC;4EAAI,WAAU;;;;;;;;;;;;8EAEjB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;;;;;sFACf,8OAAC;4EAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,QAAQ;4CAAC;4CAAG;yCAAI;oCAAC;oCAC5B,YAAY;wCAAE,UAAU;wCAAI,QAAQ;wCAAU,MAAM;oCAAS;oCAC7D,WAAU;8CAEV,cAAA,8OAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvC;uCAEe", "debugId": null}}, {"offset": {"line": 1203, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/site%20web%20edara/src/components/WorkTogether.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { ArrowRight } from 'lucide-react'\n\nconst WorkTogether = () => {\n  const collaborators = [\n    { id: 1, name: '<PERSON>', color: 'bg-pink-400', position: { top: '20%', left: '30%' } },\n    { id: 2, name: '<PERSON>', color: 'bg-blue-400', position: { top: '10%', left: '60%' } },\n    { id: 3, name: '<PERSON>', color: 'bg-green-400', position: { top: '40%', left: '80%' } },\n    { id: 4, name: '<PERSON>', color: 'bg-purple-400', position: { top: '70%', left: '70%' } },\n    { id: 5, name: '<PERSON>', color: 'bg-yellow-400', position: { top: '80%', left: '40%' } },\n    { id: 6, name: '<PERSON>', color: 'bg-red-400', position: { top: '60%', left: '10%' } },\n    { id: 7, name: '<PERSON>', color: 'bg-indigo-400', position: { top: '30%', left: '5%' } },\n  ]\n\n  return (\n    <section className=\"section-padding bg-gray-50\">\n      <div className=\"container-custom\">\n        <div className=\"grid lg:grid-cols-2 gap-16 items-center\">\n          {/* Left Content - Collaboration Visualization */}\n          <motion.div\n            initial={{ opacity: 0, x: -50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"relative h-96 lg:h-[500px]\"\n          >\n            {/* Central Hub */}\n            <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-24 h-24 bg-white rounded-full shadow-xl flex items-center justify-center border-4 border-blue-200\">\n              <div className=\"w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center\">\n                <span className=\"text-white font-bold text-lg\">W</span>\n              </div>\n            </div>\n\n            {/* Collaborator Avatars */}\n            {collaborators.map((collaborator, index) => (\n              <motion.div\n                key={collaborator.id}\n                initial={{ opacity: 0, scale: 0 }}\n                whileInView={{ opacity: 1, scale: 1 }}\n                transition={{ duration: 0.5, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                className=\"absolute\"\n                style={collaborator.position}\n              >\n                <motion.div\n                  animate={{ \n                    y: [0, -10, 0],\n                    rotate: [0, 5, -5, 0]\n                  }}\n                  transition={{ \n                    duration: 3 + index * 0.5, \n                    repeat: Infinity,\n                    ease: \"easeInOut\"\n                  }}\n                  className={`w-12 h-12 ${collaborator.color} rounded-full shadow-lg flex items-center justify-center text-white font-semibold cursor-pointer hover:scale-110 transition-transform duration-200`}\n                >\n                  {collaborator.name.charAt(0)}\n                </motion.div>\n                \n                {/* Connection Lines */}\n                <svg \n                  className=\"absolute top-6 left-6 pointer-events-none\"\n                  style={{\n                    width: '200px',\n                    height: '200px',\n                    transform: 'translate(-50%, -50%)'\n                  }}\n                >\n                  <motion.line\n                    initial={{ pathLength: 0, opacity: 0 }}\n                    whileInView={{ pathLength: 1, opacity: 0.3 }}\n                    transition={{ duration: 1, delay: index * 0.2 }}\n                    viewport={{ once: true }}\n                    x1=\"0\"\n                    y1=\"0\"\n                    x2=\"100\"\n                    y2=\"100\"\n                    stroke=\"#3B82F6\"\n                    strokeWidth=\"2\"\n                    strokeDasharray=\"5,5\"\n                  />\n                </svg>\n              </motion.div>\n            ))}\n\n            {/* Floating Elements */}\n            <motion.div\n              animate={{ \n                rotate: [0, 360],\n                scale: [1, 1.1, 1]\n              }}\n              transition={{ \n                duration: 8, \n                repeat: Infinity,\n                ease: \"linear\"\n              }}\n              className=\"absolute top-4 right-4 w-6 h-6 bg-yellow-400 rounded-full shadow-lg\"\n            ></motion.div>\n            \n            <motion.div\n              animate={{ \n                rotate: [360, 0],\n                scale: [1, 1.2, 1]\n              }}\n              transition={{ \n                duration: 6, \n                repeat: Infinity,\n                ease: \"linear\"\n              }}\n              className=\"absolute bottom-4 left-4 w-8 h-8 bg-pink-400 rounded-full shadow-lg\"\n            ></motion.div>\n          </motion.div>\n\n          {/* Right Content */}\n          <motion.div\n            initial={{ opacity: 0, x: 50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            viewport={{ once: true }}\n            className=\"space-y-8\"\n          >\n            <div className=\"space-y-6\">\n              <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 leading-tight\">\n                Work{' '}\n                <span className=\"text-blue-600\">together</span>\n              </h2>\n              <p className=\"text-lg text-gray-600 leading-relaxed\">\n                With whitespace, share your notes with your colleagues and collaborate on them. \n                You can also publish a note to the internet and share the URL with others.\n              </p>\n            </div>\n\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-2 h-2 bg-blue-600 rounded-full\"></div>\n                <span className=\"text-gray-700\">Real-time collaboration</span>\n              </div>\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-2 h-2 bg-blue-600 rounded-full\"></div>\n                <span className=\"text-gray-700\">Share notes instantly</span>\n              </div>\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-2 h-2 bg-blue-600 rounded-full\"></div>\n                <span className=\"text-gray-700\">Publish to the web</span>\n              </div>\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-2 h-2 bg-blue-600 rounded-full\"></div>\n                <span className=\"text-gray-700\">Team permissions</span>\n              </div>\n            </div>\n\n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              className=\"btn-primary flex items-center space-x-2 group\"\n            >\n              <span>Try it now</span>\n              <ArrowRight className=\"w-5 h-5 group-hover:translate-x-1 transition-transform duration-200\" />\n            </motion.button>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  )\n}\n\nexport default WorkTogether\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,eAAe;IACnB,MAAM,gBAAgB;QACpB;YAAE,IAAI;YAAG,MAAM;YAAS,OAAO;YAAe,UAAU;gBAAE,KAAK;gBAAO,MAAM;YAAM;QAAE;QACpF;YAAE,IAAI;YAAG,MAAM;YAAO,OAAO;YAAe,UAAU;gBAAE,KAAK;gBAAO,MAAM;YAAM;QAAE;QAClF;YAAE,IAAI;YAAG,MAAM;YAAS,OAAO;YAAgB,UAAU;gBAAE,KAAK;gBAAO,MAAM;YAAM;QAAE;QACrF;YAAE,IAAI;YAAG,MAAM;YAAS,OAAO;YAAiB,UAAU;gBAAE,KAAK;gBAAO,MAAM;YAAM;QAAE;QACtF;YAAE,IAAI;YAAG,MAAM;YAAO,OAAO;YAAiB,UAAU;gBAAE,KAAK;gBAAO,MAAM;YAAM;QAAE;QACpF;YAAE,IAAI;YAAG,MAAM;YAAS,OAAO;YAAc,UAAU;gBAAE,KAAK;gBAAO,MAAM;YAAM;QAAE;QACnF;YAAE,IAAI;YAAG,MAAM;YAAS,OAAO;YAAiB,UAAU;gBAAE,KAAK;gBAAO,MAAM;YAAK;QAAE;KACtF;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAGV,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;;;;;;4BAKlD,cAAc,GAAG,CAAC,CAAC,cAAc,sBAChC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,aAAa;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCACpC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;oCACV,OAAO,aAAa,QAAQ;;sDAE5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDACP,GAAG;oDAAC;oDAAG,CAAC;oDAAI;iDAAE;gDACd,QAAQ;oDAAC;oDAAG;oDAAG,CAAC;oDAAG;iDAAE;4CACvB;4CACA,YAAY;gDACV,UAAU,IAAI,QAAQ;gDACtB,QAAQ;gDACR,MAAM;4CACR;4CACA,WAAW,CAAC,UAAU,EAAE,aAAa,KAAK,CAAC,kJAAkJ,CAAC;sDAE7L,aAAa,IAAI,CAAC,MAAM,CAAC;;;;;;sDAI5B,8OAAC;4CACC,WAAU;4CACV,OAAO;gDACL,OAAO;gDACP,QAAQ;gDACR,WAAW;4CACb;sDAEA,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;gDACV,SAAS;oDAAE,YAAY;oDAAG,SAAS;gDAAE;gDACrC,aAAa;oDAAE,YAAY;oDAAG,SAAS;gDAAI;gDAC3C,YAAY;oDAAE,UAAU;oDAAG,OAAO,QAAQ;gDAAI;gDAC9C,UAAU;oDAAE,MAAM;gDAAK;gDACvB,IAAG;gDACH,IAAG;gDACH,IAAG;gDACH,IAAG;gDACH,QAAO;gDACP,aAAY;gDACZ,iBAAgB;;;;;;;;;;;;mCA3Cf,aAAa,EAAE;;;;;0CAkDxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCACP,QAAQ;wCAAC;wCAAG;qCAAI;oCAChB,OAAO;wCAAC;wCAAG;wCAAK;qCAAE;gCACpB;gCACA,YAAY;oCACV,UAAU;oCACV,QAAQ;oCACR,MAAM;gCACR;gCACA,WAAU;;;;;;0CAGZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCACP,QAAQ;wCAAC;wCAAK;qCAAE;oCAChB,OAAO;wCAAC;wCAAG;wCAAK;qCAAE;gCACpB;gCACA,YAAY;oCACV,UAAU;oCACV,QAAQ;oCACR,MAAM;gCACR;gCACA,WAAU;;;;;;;;;;;;kCAKd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;;4CAAyE;4CAChF;0DACL,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;kDAElC,8OAAC;wCAAE,WAAU;kDAAwC;;;;;;;;;;;;0CAMvD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;kDAElC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;kDAElC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;kDAElC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;0CAIpC,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAU;;kDAEV,8OAAC;kDAAK;;;;;;kDACN,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC;uCAEe", "debugId": null}}, {"offset": {"line": 1686, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/site%20web%20edara/src/components/UseAsExtension.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { ArrowRight, Download, Chrome, Globe } from 'lucide-react'\n\nconst UseAsExtension = () => {\n  const browsers = [\n    { name: 'Chrome', icon: Chrome, users: '2M+' },\n    { name: 'Firefox', icon: Globe, users: '500K+' },\n    { name: 'Safari', icon: Globe, users: '300K+' },\n  ]\n\n  return (\n    <section className=\"section-padding bg-gray-50\">\n      <div className=\"container-custom\">\n        <div className=\"grid lg:grid-cols-2 gap-16 items-center\">\n          {/* Left Content */}\n          <motion.div\n            initial={{ opacity: 0, x: -50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"space-y-8\"\n          >\n            <div className=\"space-y-6\">\n              <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 leading-tight\">\n                Use as{' '}\n                <span className=\"text-blue-600\">Extension</span>\n              </h2>\n              <p className=\"text-lg text-gray-600 leading-relaxed\">\n                Use the web clipper extension, available on all major browsers, to save web pages\n                or take screenshots as notes. Access Whitespace quickly from your browser toolbar.\n              </p>\n            </div>\n\n            <div className=\"space-y-4\">\n              <h3 className=\"text-xl font-semibold text-gray-900\">Available for:</h3>\n              <div className=\"grid sm:grid-cols-3 gap-4\">\n                {browsers.map((browser, index) => (\n                  <motion.div\n                    key={browser.name}\n                    initial={{ opacity: 0, y: 20 }}\n                    whileInView={{ opacity: 1, y: 0 }}\n                    transition={{ duration: 0.5, delay: index * 0.1 }}\n                    viewport={{ once: true }}\n                    className=\"flex items-center space-x-3 p-4 bg-white rounded-lg border border-gray-200 hover:border-blue-300 transition-colors duration-200\"\n                  >\n                    <browser.icon className=\"w-8 h-8 text-gray-700\" />\n                    <div>\n                      <div className=\"font-medium text-gray-900\">{browser.name}</div>\n                      <div className=\"text-sm text-gray-500\">{browser.users} users</div>\n                    </div>\n                  </motion.div>\n                ))}\n              </div>\n            </div>\n\n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              className=\"btn-primary flex items-center space-x-2 group\"\n            >\n              <Download className=\"w-5 h-5\" />\n              <span>Install Extension</span>\n              <ArrowRight className=\"w-5 h-5 group-hover:translate-x-1 transition-transform duration-200\" />\n            </motion.button>\n          </motion.div>\n\n          {/* Right Content - Extension Mockup */}\n          <motion.div\n            initial={{ opacity: 0, x: 50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            viewport={{ once: true }}\n            className=\"relative\"\n          >\n            <div className=\"relative bg-white rounded-2xl shadow-2xl p-6 transform -rotate-3 hover:rotate-0 transition-transform duration-500\">\n              {/* Browser Window Mockup */}\n              <div className=\"space-y-4\">\n                {/* Browser Header */}\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex space-x-2\">\n                    <div className=\"w-3 h-3 bg-red-400 rounded-full\"></div>\n                    <div className=\"w-3 h-3 bg-yellow-400 rounded-full\"></div>\n                    <div className=\"w-3 h-3 bg-green-400 rounded-full\"></div>\n                  </div>\n                  <div className=\"text-xs text-gray-400\">browser.com</div>\n                </div>\n\n                {/* Extension Popup */}\n                <div className=\"bg-gray-50 rounded-lg p-4 space-y-3\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                      <span className=\"text-white font-bold text-sm\">W</span>\n                    </div>\n                    <div>\n                      <div className=\"font-semibold text-gray-900\">Whitespace</div>\n                      <div className=\"text-xs text-gray-500\">Web Clipper</div>\n                    </div>\n                  </div>\n\n                  <div className=\"space-y-2\">\n                    <button className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-lg text-sm font-medium\">\n                      Save Page\n                    </button>\n                    <button className=\"w-full bg-gray-200 text-gray-700 py-2 px-4 rounded-lg text-sm font-medium\">\n                      Take Screenshot\n                    </button>\n                    <button className=\"w-full bg-gray-200 text-gray-700 py-2 px-4 rounded-lg text-sm font-medium\">\n                      Quick Note\n                    </button>\n                  </div>\n                </div>\n              </div>\n\n              {/* Floating Elements */}\n              <motion.div\n                animate={{ y: [-5, 5, -5] }}\n                transition={{ duration: 2, repeat: Infinity }}\n                className=\"absolute -top-3 -right-3 w-6 h-6 bg-blue-500 rounded-full shadow-lg\"\n              ></motion.div>\n\n              <motion.div\n                animate={{ y: [5, -5, 5] }}\n                transition={{ duration: 3, repeat: Infinity }}\n                className=\"absolute -bottom-3 -left-3 w-4 h-4 bg-yellow-400 rounded-full shadow-lg\"\n              ></motion.div>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  )\n}\n\nexport default UseAsExtension\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,iBAAiB;IACrB,MAAM,WAAW;QACf;YAAE,MAAM;YAAU,MAAM,sMAAA,CAAA,SAAM;YAAE,OAAO;QAAM;QAC7C;YAAE,MAAM;YAAW,MAAM,oMAAA,CAAA,QAAK;YAAE,OAAO;QAAQ;QAC/C;YAAE,MAAM;YAAU,MAAM,oMAAA,CAAA,QAAK;YAAE,OAAO;QAAQ;KAC/C;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;;4CAAyE;4CAC9E;0DACP,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;kDAElC,8OAAC;wCAAE,WAAU;kDAAwC;;;;;;;;;;;;0CAMvD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAsC;;;;;;kDACpD,8OAAC;wCAAI,WAAU;kDACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,aAAa;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAChC,YAAY;oDAAE,UAAU;oDAAK,OAAO,QAAQ;gDAAI;gDAChD,UAAU;oDAAE,MAAM;gDAAK;gDACvB,WAAU;;kEAEV,8OAAC,QAAQ,IAAI;wDAAC,WAAU;;;;;;kEACxB,8OAAC;;0EACC,8OAAC;gEAAI,WAAU;0EAA6B,QAAQ,IAAI;;;;;;0EACxD,8OAAC;gEAAI,WAAU;;oEAAyB,QAAQ,KAAK;oEAAC;;;;;;;;;;;;;;+CAVnD,QAAQ,IAAI;;;;;;;;;;;;;;;;0CAiBzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAU;;kDAEV,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;kDAAK;;;;;;kDACN,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;kCAK1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;;;;;;;8DAEjB,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAIzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;0EAA+B;;;;;;;;;;;sEAEjD,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;8EAA8B;;;;;;8EAC7C,8OAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;8DAI3C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAO,WAAU;sEAAyE;;;;;;sEAG3F,8OAAC;4DAAO,WAAU;sEAA4E;;;;;;sEAG9F,8OAAC;4DAAO,WAAU;sEAA4E;;;;;;;;;;;;;;;;;;;;;;;;8CAQpG,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,GAAG;4CAAC,CAAC;4CAAG;4CAAG,CAAC;yCAAE;oCAAC;oCAC1B,YAAY;wCAAE,UAAU;wCAAG,QAAQ;oCAAS;oCAC5C,WAAU;;;;;;8CAGZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,GAAG;4CAAC;4CAAG,CAAC;4CAAG;yCAAE;oCAAC;oCACzB,YAAY;wCAAE,UAAU;wCAAG,QAAQ;oCAAS;oCAC5C,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1B;uCAEe", "debugId": null}}, {"offset": {"line": 2138, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/site%20web%20edara/src/components/Customize.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { ArrowR<PERSON>, Palette, Layout, Setting<PERSON>, Zap } from 'lucide-react'\n\nconst Customize = () => {\n  const customizations = [\n    {\n      icon: Palette,\n      title: 'Themes & Colors',\n      description: 'Choose from multiple themes and customize colors to match your brand'\n    },\n    {\n      icon: Layout,\n      title: 'Layout Options',\n      description: 'Arrange your workspace exactly how you want it with flexible layouts'\n    },\n    {\n      icon: Settings,\n      title: 'Advanced Settings',\n      description: 'Fine-tune every aspect of your workspace with powerful settings'\n    },\n    {\n      icon: Zap,\n      title: 'Workflow Automation',\n      description: 'Create custom workflows and automate repetitive tasks'\n    }\n  ]\n\n  return (\n    <section className=\"section-padding bg-white\">\n      <div className=\"container-custom\">\n        <div className=\"grid lg:grid-cols-2 gap-16 items-center\">\n          {/* Left Content - Customization Interface */}\n          <motion.div\n            initial={{ opacity: 0, x: -50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"relative\"\n          >\n            <div className=\"relative bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl p-8\">\n              {/* Customization Panel Mockup */}\n              <div className=\"bg-white rounded-xl shadow-lg p-6 space-y-6\">\n                <div className=\"flex items-center justify-between\">\n                  <h3 className=\"font-semibold text-gray-900\">Customize Workspace</h3>\n                  <Settings className=\"w-5 h-5 text-gray-400\" />\n                </div>\n                \n                {/* Theme Selection */}\n                <div className=\"space-y-3\">\n                  <div className=\"text-sm font-medium text-gray-700\">Theme</div>\n                  <div className=\"flex space-x-3\">\n                    <motion.div\n                      whileHover={{ scale: 1.05 }}\n                      className=\"w-12 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg cursor-pointer border-2 border-blue-500\"\n                    ></motion.div>\n                    <motion.div\n                      whileHover={{ scale: 1.05 }}\n                      className=\"w-12 h-8 bg-gradient-to-r from-green-400 to-blue-500 rounded-lg cursor-pointer border-2 border-transparent hover:border-gray-300\"\n                    ></motion.div>\n                    <motion.div\n                      whileHover={{ scale: 1.05 }}\n                      className=\"w-12 h-8 bg-gradient-to-r from-pink-400 to-red-500 rounded-lg cursor-pointer border-2 border-transparent hover:border-gray-300\"\n                    ></motion.div>\n                    <motion.div\n                      whileHover={{ scale: 1.05 }}\n                      className=\"w-12 h-8 bg-gradient-to-r from-gray-700 to-gray-900 rounded-lg cursor-pointer border-2 border-transparent hover:border-gray-300\"\n                    ></motion.div>\n                  </div>\n                </div>\n                \n                {/* Layout Options */}\n                <div className=\"space-y-3\">\n                  <div className=\"text-sm font-medium text-gray-700\">Layout</div>\n                  <div className=\"grid grid-cols-3 gap-2\">\n                    <motion.div\n                      whileHover={{ scale: 1.05 }}\n                      className=\"aspect-square bg-blue-100 rounded-lg border-2 border-blue-500 cursor-pointer flex items-center justify-center\"\n                    >\n                      <Layout className=\"w-4 h-4 text-blue-600\" />\n                    </motion.div>\n                    <motion.div\n                      whileHover={{ scale: 1.05 }}\n                      className=\"aspect-square bg-gray-100 rounded-lg border-2 border-transparent hover:border-gray-300 cursor-pointer flex items-center justify-center\"\n                    >\n                      <Layout className=\"w-4 h-4 text-gray-400\" />\n                    </motion.div>\n                    <motion.div\n                      whileHover={{ scale: 1.05 }}\n                      className=\"aspect-square bg-gray-100 rounded-lg border-2 border-transparent hover:border-gray-300 cursor-pointer flex items-center justify-center\"\n                    >\n                      <Layout className=\"w-4 h-4 text-gray-400\" />\n                    </motion.div>\n                  </div>\n                </div>\n                \n                {/* Settings Toggles */}\n                <div className=\"space-y-3\">\n                  <div className=\"text-sm font-medium text-gray-700\">Features</div>\n                  <div className=\"space-y-2\">\n                    {['Dark mode', 'Notifications', 'Auto-save'].map((feature, index) => (\n                      <div key={feature} className=\"flex items-center justify-between\">\n                        <span className=\"text-sm text-gray-600\">{feature}</span>\n                        <motion.div\n                          whileHover={{ scale: 1.05 }}\n                          className={`w-10 h-6 rounded-full cursor-pointer transition-colors duration-200 ${\n                            index === 0 ? 'bg-blue-500' : 'bg-gray-300'\n                          }`}\n                        >\n                          <motion.div\n                            animate={{ x: index === 0 ? 16 : 2 }}\n                            className=\"w-5 h-5 bg-white rounded-full mt-0.5 shadow-sm\"\n                          ></motion.div>\n                        </motion.div>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              </div>\n              \n              {/* Floating Elements */}\n              <motion.div\n                animate={{ rotate: [0, 360] }}\n                transition={{ duration: 10, repeat: Infinity, ease: \"linear\" }}\n                className=\"absolute -top-4 -right-4 w-8 h-8 bg-purple-500 rounded-full shadow-lg flex items-center justify-center\"\n              >\n                <Palette className=\"w-4 h-4 text-white\" />\n              </motion.div>\n            </div>\n          </motion.div>\n\n          {/* Right Content */}\n          <motion.div\n            initial={{ opacity: 0, x: 50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            viewport={{ once: true }}\n            className=\"space-y-8\"\n          >\n            <div className=\"space-y-6\">\n              <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 leading-tight\">\n                Customise it to{' '}\n                <span className=\"text-blue-600\">your needs</span>\n              </h2>\n              <p className=\"text-lg text-gray-600 leading-relaxed\">\n                Customise the app with plugins, custom themes and multiple text editors (Rich Text or Markdown). \n                Or create your own scripts and plugins using the Extension API.\n              </p>\n            </div>\n\n            <div className=\"space-y-6\">\n              {customizations.map((item, index) => (\n                <motion.div\n                  key={item.title}\n                  initial={{ opacity: 0, y: 20 }}\n                  whileInView={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.5, delay: index * 0.1 }}\n                  viewport={{ once: true }}\n                  className=\"flex items-start space-x-4\"\n                >\n                  <div className=\"flex-shrink-0 w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center\">\n                    <item.icon className=\"w-6 h-6 text-blue-600\" />\n                  </div>\n                  <div>\n                    <h3 className=\"font-semibold text-gray-900 mb-2\">{item.title}</h3>\n                    <p className=\"text-gray-600\">{item.description}</p>\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n\n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              className=\"btn-primary flex items-center space-x-2 group\"\n            >\n              <span>Let's Go</span>\n              <ArrowRight className=\"w-5 h-5 group-hover:translate-x-1 transition-transform duration-200\" />\n            </motion.button>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  )\n}\n\nexport default Customize\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,YAAY;IAChB,MAAM,iBAAiB;QACrB;YACE,MAAM,wMAAA,CAAA,UAAO;YACb,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,qNAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,0MAAA,CAAA,WAAQ;YACd,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,gMAAA,CAAA,MAAG;YACT,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA8B;;;;;;8DAC5C,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;sDAItB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAoC;;;;;;8DACnD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,YAAY;gEAAE,OAAO;4DAAK;4DAC1B,WAAU;;;;;;sEAEZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,YAAY;gEAAE,OAAO;4DAAK;4DAC1B,WAAU;;;;;;sEAEZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,YAAY;gEAAE,OAAO;4DAAK;4DAC1B,WAAU;;;;;;sEAEZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,YAAY;gEAAE,OAAO;4DAAK;4DAC1B,WAAU;;;;;;;;;;;;;;;;;;sDAMhB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAoC;;;;;;8DACnD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,YAAY;gEAAE,OAAO;4DAAK;4DAC1B,WAAU;sEAEV,cAAA,8OAAC,qNAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;;;;;;sEAEpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,YAAY;gEAAE,OAAO;4DAAK;4DAC1B,WAAU;sEAEV,cAAA,8OAAC,qNAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;;;;;;sEAEpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,YAAY;gEAAE,OAAO;4DAAK;4DAC1B,WAAU;sEAEV,cAAA,8OAAC,qNAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sDAMxB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAoC;;;;;;8DACnD,8OAAC;oDAAI,WAAU;8DACZ;wDAAC;wDAAa;wDAAiB;qDAAY,CAAC,GAAG,CAAC,CAAC,SAAS,sBACzD,8OAAC;4DAAkB,WAAU;;8EAC3B,8OAAC;oEAAK,WAAU;8EAAyB;;;;;;8EACzC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oEACT,YAAY;wEAAE,OAAO;oEAAK;oEAC1B,WAAW,CAAC,oEAAoE,EAC9E,UAAU,IAAI,gBAAgB,eAC9B;8EAEF,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wEACT,SAAS;4EAAE,GAAG,UAAU,IAAI,KAAK;wEAAE;wEACnC,WAAU;;;;;;;;;;;;2DAVN;;;;;;;;;;;;;;;;;;;;;;8CAoBlB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,QAAQ;4CAAC;4CAAG;yCAAI;oCAAC;oCAC5B,YAAY;wCAAE,UAAU;wCAAI,QAAQ;wCAAU,MAAM;oCAAS;oCAC7D,WAAU;8CAEV,cAAA,8OAAC,wMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAMzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;;4CAAyE;4CACrE;0DAChB,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;kDAElC,8OAAC;wCAAE,WAAU;kDAAwC;;;;;;;;;;;;0CAMvD,8OAAC;gCAAI,WAAU;0CACZ,eAAe,GAAG,CAAC,CAAC,MAAM,sBACzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO,QAAQ;wCAAI;wCAChD,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;;;;;;0DAEvB,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAoC,KAAK,KAAK;;;;;;kEAC5D,8OAAC;wDAAE,WAAU;kEAAiB,KAAK,WAAW;;;;;;;;;;;;;uCAZ3C,KAAK,KAAK;;;;;;;;;;0CAkBrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAU;;kDAEV,8OAAC;kDAAK;;;;;;kDACN,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC;uCAEe", "debugId": null}}, {"offset": {"line": 2660, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/site%20web%20edara/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 2681, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/site%20web%20edara/src/components/Pricing.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { Check, Star } from 'lucide-react'\nimport { cn } from '@/lib/utils'\nimport pricingData from '@/data/pricing.json'\n\nconst Pricing = () => {\n  return (\n    <section id=\"pricing\" className=\"section-padding bg-white\">\n      <div className=\"container-custom\">\n        {/* Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center space-y-6 mb-16\"\n        >\n          <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900\">\n            Choose Your Plan\n          </h2>\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n            Whether you want to get organized, keep your personal life on track, \n            or boost workplace productivity, Ever<PERSON> has the right plan for you.\n          </p>\n        </motion.div>\n\n        {/* Pricing Cards */}\n        <div className=\"grid md:grid-cols-3 gap-8 max-w-6xl mx-auto\">\n          {pricingData.plans.map((plan, index) => (\n            <motion.div\n              key={plan.id}\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5, delay: index * 0.1 }}\n              viewport={{ once: true }}\n              className={cn(\n                \"relative bg-white rounded-2xl border-2 p-8 transition-all duration-300 hover:shadow-xl\",\n                plan.popular \n                  ? \"border-blue-500 shadow-lg scale-105\" \n                  : \"border-gray-200 hover:border-blue-300\"\n              )}\n            >\n              {/* Popular Badge */}\n              {plan.popular && (\n                <div className=\"absolute -top-4 left-1/2 transform -translate-x-1/2\">\n                  <div className=\"bg-blue-600 text-white px-4 py-2 rounded-full text-sm font-medium flex items-center space-x-1\">\n                    <Star className=\"w-4 h-4 fill-current\" />\n                    <span>Most Popular</span>\n                  </div>\n                </div>\n              )}\n\n              <div className=\"space-y-6\">\n                {/* Plan Header */}\n                <div className=\"text-center space-y-2\">\n                  <h3 className=\"text-2xl font-bold text-gray-900\">{plan.name}</h3>\n                  <p className=\"text-gray-600\">{plan.description}</p>\n                </div>\n\n                {/* Price */}\n                <div className=\"text-center space-y-1\">\n                  <div className=\"flex items-baseline justify-center space-x-1\">\n                    <span className=\"text-4xl font-bold text-gray-900\">{plan.price}</span>\n                    <span className=\"text-gray-600\">{plan.period}</span>\n                  </div>\n                </div>\n\n                {/* Features */}\n                <div className=\"space-y-4\">\n                  {plan.features.map((feature, featureIndex) => (\n                    <motion.div\n                      key={featureIndex}\n                      initial={{ opacity: 0, x: -20 }}\n                      whileInView={{ opacity: 1, x: 0 }}\n                      transition={{ duration: 0.3, delay: featureIndex * 0.05 }}\n                      viewport={{ once: true }}\n                      className=\"flex items-start space-x-3\"\n                    >\n                      <div className=\"flex-shrink-0 w-5 h-5 bg-green-100 rounded-full flex items-center justify-center mt-0.5\">\n                        <Check className=\"w-3 h-3 text-green-600\" />\n                      </div>\n                      <span className=\"text-gray-700 text-sm leading-relaxed\">{feature}</span>\n                    </motion.div>\n                  ))}\n                </div>\n\n                {/* CTA Button */}\n                <motion.button\n                  whileHover={{ scale: 1.02 }}\n                  whileTap={{ scale: 0.98 }}\n                  className={cn(\n                    \"w-full py-3 px-6 rounded-lg font-medium transition-colors duration-200\",\n                    plan.buttonVariant === 'primary'\n                      ? \"bg-blue-600 hover:bg-blue-700 text-white\"\n                      : \"bg-gray-100 hover:bg-gray-200 text-gray-900 border border-gray-200\"\n                  )}\n                >\n                  {plan.buttonText}\n                </motion.button>\n              </div>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Bottom Note */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.3 }}\n          viewport={{ once: true }}\n          className=\"text-center mt-12\"\n        >\n          <p className=\"text-gray-600\">\n            All plans include a 30-day free trial. No credit card required.\n          </p>\n        </motion.div>\n      </div>\n    </section>\n  )\n}\n\nexport default Pricing\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AALA;;;;;;AAOA,MAAM,UAAU;IACd,qBACE,8OAAC;QAAQ,IAAG;QAAU,WAAU;kBAC9B,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAA2D;;;;;;sCAGzE,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAOzD,8OAAC;oBAAI,WAAU;8BACZ,8FAAA,CAAA,UAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0FACA,KAAK,OAAO,GACR,wCACA;;gCAIL,KAAK,OAAO,kBACX,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;0DAAK;;;;;;;;;;;;;;;;;8CAKZ,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAoC,KAAK,IAAI;;;;;;8DAC3D,8OAAC;oDAAE,WAAU;8DAAiB,KAAK,WAAW;;;;;;;;;;;;sDAIhD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAoC,KAAK,KAAK;;;;;;kEAC9D,8OAAC;wDAAK,WAAU;kEAAiB,KAAK,MAAM;;;;;;;;;;;;;;;;;sDAKhD,8OAAC;4CAAI,WAAU;sDACZ,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,6BAC3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDAET,SAAS;wDAAE,SAAS;wDAAG,GAAG,CAAC;oDAAG;oDAC9B,aAAa;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAChC,YAAY;wDAAE,UAAU;wDAAK,OAAO,eAAe;oDAAK;oDACxD,UAAU;wDAAE,MAAM;oDAAK;oDACvB,WAAU;;sEAEV,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;sEAEnB,8OAAC;4DAAK,WAAU;sEAAyC;;;;;;;mDAVpD;;;;;;;;;;sDAgBX,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4CACZ,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;4CACxB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0EACA,KAAK,aAAa,KAAK,YACnB,6CACA;sDAGL,KAAK,UAAU;;;;;;;;;;;;;2BAnEf,KAAK,EAAE;;;;;;;;;;8BA2ElB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;;;;;AAOvC;uCAEe", "debugId": null}}, {"offset": {"line": 2985, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/site%20web%20edara/src/components/MobileSection.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { ArrowRight, Smartphone, Tablet, Monitor, Wifi } from 'lucide-react'\n\nconst MobileSection = () => {\n  const devices = [\n    { icon: Smartphone, name: 'Mobile', description: 'iOS & Android apps' },\n    { icon: Tablet, name: 'Tablet', description: 'Optimized for tablets' },\n    { icon: Monitor, name: 'Desktop', description: 'Windows, Mac & Linux' },\n  ]\n\n  return (\n    <section className=\"section-padding bg-gradient-to-br from-blue-600 to-indigo-700 text-white relative overflow-hidden\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0\">\n        <motion.div\n          animate={{ \n            rotate: [0, 360],\n            scale: [1, 1.2, 1]\n          }}\n          transition={{ \n            duration: 20, \n            repeat: Infinity,\n            ease: \"linear\"\n          }}\n          className=\"absolute -top-24 -left-24 w-48 h-48 bg-white/10 rounded-full\"\n        ></motion.div>\n        \n        <motion.div\n          animate={{ \n            rotate: [360, 0],\n            scale: [1, 1.1, 1]\n          }}\n          transition={{ \n            duration: 15, \n            repeat: Infinity,\n            ease: \"linear\"\n          }}\n          className=\"absolute -bottom-16 -right-16 w-32 h-32 bg-white/10 rounded-full\"\n        ></motion.div>\n      </div>\n\n      <div className=\"container-custom relative z-10\">\n        <div className=\"grid lg:grid-cols-2 gap-16 items-center\">\n          {/* Left Content */}\n          <motion.div\n            initial={{ opacity: 0, x: -50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"space-y-8\"\n          >\n            <div className=\"space-y-6\">\n              <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-bold leading-tight\">\n                Your work, everywhere you are\n              </h2>\n              <p className=\"text-lg text-blue-100 leading-relaxed\">\n                Access your notes from your computer, phone or tablet by synchronising with various services, \n                including whitespace, Dropbox and OneDrive. The app is available on Windows, macOS, Linux, \n                Android and iOS. A terminal app is also available!\n              </p>\n            </div>\n\n            <div className=\"space-y-6\">\n              <h3 className=\"text-xl font-semibold\">Available on all platforms:</h3>\n              <div className=\"grid sm:grid-cols-3 gap-4\">\n                {devices.map((device, index) => (\n                  <motion.div\n                    key={device.name}\n                    initial={{ opacity: 0, y: 20 }}\n                    whileInView={{ opacity: 1, y: 0 }}\n                    transition={{ duration: 0.5, delay: index * 0.1 }}\n                    viewport={{ once: true }}\n                    className=\"flex flex-col items-center space-y-3 p-6 bg-white/10 rounded-xl backdrop-blur-sm hover:bg-white/20 transition-colors duration-200\"\n                  >\n                    <device.icon className=\"w-8 h-8\" />\n                    <div className=\"text-center\">\n                      <div className=\"font-medium\">{device.name}</div>\n                      <div className=\"text-sm text-blue-200\">{device.description}</div>\n                    </div>\n                  </motion.div>\n                ))}\n              </div>\n            </div>\n\n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              className=\"bg-white text-blue-600 font-semibold px-8 py-4 rounded-lg hover:bg-gray-50 transition-colors duration-200 flex items-center space-x-2 group\"\n            >\n              <span>Try Taskey</span>\n              <ArrowRight className=\"w-5 h-5 group-hover:translate-x-1 transition-transform duration-200\" />\n            </motion.button>\n          </motion.div>\n\n          {/* Right Content - Device Mockups */}\n          <motion.div\n            initial={{ opacity: 0, x: 50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            viewport={{ once: true }}\n            className=\"relative\"\n          >\n            <div className=\"relative\">\n              {/* Phone Mockup */}\n              <motion.div\n                animate={{ y: [-10, 10, -10] }}\n                transition={{ duration: 4, repeat: Infinity }}\n                className=\"relative z-20 bg-white rounded-3xl p-2 shadow-2xl transform rotate-12 hover:rotate-6 transition-transform duration-500\"\n                style={{ width: '200px', height: '400px' }}\n              >\n                <div className=\"bg-gray-900 rounded-2xl h-full p-4 flex flex-col\">\n                  {/* Phone Header */}\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <div className=\"text-white text-xs\">9:41</div>\n                    <div className=\"flex space-x-1\">\n                      <div className=\"w-1 h-1 bg-white rounded-full\"></div>\n                      <div className=\"w-1 h-1 bg-white rounded-full\"></div>\n                      <div className=\"w-1 h-1 bg-white rounded-full\"></div>\n                    </div>\n                  </div>\n                  \n                  {/* App Content */}\n                  <div className=\"flex-1 space-y-3\">\n                    <div className=\"bg-blue-600 rounded-lg p-3\">\n                      <div className=\"w-8 h-8 bg-white rounded-lg mb-2 flex items-center justify-center\">\n                        <span className=\"text-blue-600 font-bold text-sm\">W</span>\n                      </div>\n                      <div className=\"text-white text-xs\">Whitespace</div>\n                    </div>\n                    \n                    <div className=\"space-y-2\">\n                      <div className=\"h-2 bg-gray-700 rounded w-3/4\"></div>\n                      <div className=\"h-2 bg-gray-700 rounded w-1/2\"></div>\n                      <div className=\"h-2 bg-gray-700 rounded w-2/3\"></div>\n                    </div>\n                  </div>\n                </div>\n              </motion.div>\n\n              {/* Tablet Mockup */}\n              <motion.div\n                animate={{ y: [10, -10, 10] }}\n                transition={{ duration: 5, repeat: Infinity }}\n                className=\"absolute top-20 -left-20 z-10 bg-white rounded-2xl p-3 shadow-xl transform -rotate-12 hover:-rotate-6 transition-transform duration-500\"\n                style={{ width: '280px', height: '200px' }}\n              >\n                <div className=\"bg-gray-100 rounded-xl h-full p-4\">\n                  <div className=\"flex items-center space-x-2 mb-3\">\n                    <div className=\"w-6 h-6 bg-blue-600 rounded-lg flex items-center justify-center\">\n                      <span className=\"text-white font-bold text-xs\">W</span>\n                    </div>\n                    <div className=\"text-gray-900 text-sm font-medium\">Whitespace</div>\n                  </div>\n                  \n                  <div className=\"grid grid-cols-2 gap-2\">\n                    <div className=\"h-16 bg-blue-100 rounded-lg\"></div>\n                    <div className=\"h-16 bg-purple-100 rounded-lg\"></div>\n                  </div>\n                </div>\n              </motion.div>\n\n              {/* Sync Icon */}\n              <motion.div\n                animate={{ rotate: [0, 360] }}\n                transition={{ duration: 3, repeat: Infinity, ease: \"linear\" }}\n                className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-30 w-12 h-12 bg-yellow-400 rounded-full shadow-lg flex items-center justify-center\"\n              >\n                <Wifi className=\"w-6 h-6 text-gray-900\" />\n              </motion.div>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  )\n}\n\nexport default MobileSection\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,gBAAgB;IACpB,MAAM,UAAU;QACd;YAAE,MAAM,8MAAA,CAAA,aAAU;YAAE,MAAM;YAAU,aAAa;QAAqB;QACtE;YAAE,MAAM,sMAAA,CAAA,SAAM;YAAE,MAAM;YAAU,aAAa;QAAwB;QACrE;YAAE,MAAM,wMAAA,CAAA,UAAO;YAAE,MAAM;YAAW,aAAa;QAAuB;KACvE;IAED,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BACP,QAAQ;gCAAC;gCAAG;6BAAI;4BAChB,OAAO;gCAAC;gCAAG;gCAAK;6BAAE;wBACpB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;wBACA,WAAU;;;;;;kCAGZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BACP,QAAQ;gCAAC;gCAAK;6BAAE;4BAChB,OAAO;gCAAC;gCAAG;gCAAK;6BAAE;wBACpB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;wBACA,WAAU;;;;;;;;;;;;0BAId,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2D;;;;;;sDAGzE,8OAAC;4CAAE,WAAU;sDAAwC;;;;;;;;;;;;8CAOvD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwB;;;;;;sDACtC,8OAAC;4CAAI,WAAU;sDACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDAET,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAG;oDAC7B,aAAa;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAChC,YAAY;wDAAE,UAAU;wDAAK,OAAO,QAAQ;oDAAI;oDAChD,UAAU;wDAAE,MAAM;oDAAK;oDACvB,WAAU;;sEAEV,8OAAC,OAAO,IAAI;4DAAC,WAAU;;;;;;sEACvB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAAe,OAAO,IAAI;;;;;;8EACzC,8OAAC;oEAAI,WAAU;8EAAyB,OAAO,WAAW;;;;;;;;;;;;;mDAVvD,OAAO,IAAI;;;;;;;;;;;;;;;;8CAiBxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oCACZ,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,WAAU;;sDAEV,8OAAC;sDAAK;;;;;;sDACN,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;;;;;;;sCAK1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,GAAG;gDAAC,CAAC;gDAAI;gDAAI,CAAC;6CAAG;wCAAC;wCAC7B,YAAY;4CAAE,UAAU;4CAAG,QAAQ;wCAAS;wCAC5C,WAAU;wCACV,OAAO;4CAAE,OAAO;4CAAS,QAAQ;wCAAQ;kDAEzC,cAAA,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAqB;;;;;;sEACpC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAI,WAAU;;;;;;;;;;;;;;;;;;8DAKnB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAK,WAAU;kFAAkC;;;;;;;;;;;8EAEpD,8OAAC;oEAAI,WAAU;8EAAqB;;;;;;;;;;;;sEAGtC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,GAAG;gDAAC;gDAAI,CAAC;gDAAI;6CAAG;wCAAC;wCAC5B,YAAY;4CAAE,UAAU;4CAAG,QAAQ;wCAAS;wCAC5C,WAAU;wCACV,OAAO;4CAAE,OAAO;4CAAS,QAAQ;wCAAQ;kDAEzC,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;0EAA+B;;;;;;;;;;;sEAEjD,8OAAC;4DAAI,WAAU;sEAAoC;;;;;;;;;;;;8DAGrD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;kDAMrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,QAAQ;gDAAC;gDAAG;6CAAI;wCAAC;wCAC5B,YAAY;4CAAE,UAAU;4CAAG,QAAQ;4CAAU,MAAM;wCAAS;wCAC5D,WAAU;kDAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhC;uCAEe", "debugId": null}}, {"offset": {"line": 3553, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/site%20web%20edara/src/components/DataSecurity.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { ArrowRight, Shield, Lock, Eye, Server, Key, CheckCircle } from 'lucide-react'\n\nconst DataSecurity = () => {\n  const securityFeatures = [\n    {\n      icon: Shield,\n      title: 'End-to-End Encryption',\n      description: 'Your data is encrypted before it leaves your device'\n    },\n    {\n      icon: Lock,\n      title: 'Zero-Knowledge Architecture',\n      description: 'We cannot see your data, even if we wanted to'\n    },\n    {\n      icon: Eye,\n      title: 'Privacy First',\n      description: 'No tracking, no ads, no data mining'\n    },\n    {\n      icon: Server,\n      title: 'Secure Servers',\n      description: 'Hosted on enterprise-grade secure infrastructure'\n    }\n  ]\n\n  return (\n    <section className=\"section-padding bg-white\">\n      <div className=\"container-custom\">\n        <div className=\"grid lg:grid-cols-2 gap-16 items-center\">\n          {/* Left Content - Security Visualization */}\n          <motion.div\n            initial={{ opacity: 0, x: -50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            viewport={{ once: true }}\n            className=\"relative\"\n          >\n            <div className=\"relative bg-gradient-to-br from-green-50 to-blue-50 rounded-2xl p-8\">\n              {/* Security Dashboard Mockup */}\n              <div className=\"bg-white rounded-xl shadow-lg p-6 space-y-6\">\n                <div className=\"flex items-center justify-between\">\n                  <h3 className=\"font-semibold text-gray-900\">Security Status</h3>\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"w-3 h-3 bg-green-400 rounded-full\"></div>\n                    <span className=\"text-sm text-green-600 font-medium\">Secure</span>\n                  </div>\n                </div>\n                \n                {/* Security Metrics */}\n                <div className=\"grid grid-cols-2 gap-4\">\n                  <div className=\"bg-green-50 rounded-lg p-4 text-center\">\n                    <Shield className=\"w-8 h-8 text-green-600 mx-auto mb-2\" />\n                    <div className=\"text-2xl font-bold text-green-600\">256-bit</div>\n                    <div className=\"text-xs text-green-700\">Encryption</div>\n                  </div>\n                  \n                  <div className=\"bg-blue-50 rounded-lg p-4 text-center\">\n                    <Lock className=\"w-8 h-8 text-blue-600 mx-auto mb-2\" />\n                    <div className=\"text-2xl font-bold text-blue-600\">100%</div>\n                    <div className=\"text-xs text-blue-700\">Private</div>\n                  </div>\n                </div>\n                \n                {/* Security Checklist */}\n                <div className=\"space-y-3\">\n                  <div className=\"text-sm font-medium text-gray-700\">Security Features</div>\n                  {[\n                    'Two-factor authentication',\n                    'End-to-end encryption',\n                    'Regular security audits',\n                    'GDPR compliant'\n                  ].map((feature, index) => (\n                    <motion.div\n                      key={feature}\n                      initial={{ opacity: 0, x: -20 }}\n                      whileInView={{ opacity: 1, x: 0 }}\n                      transition={{ duration: 0.3, delay: index * 0.1 }}\n                      viewport={{ once: true }}\n                      className=\"flex items-center space-x-3\"\n                    >\n                      <CheckCircle className=\"w-4 h-4 text-green-500\" />\n                      <span className=\"text-sm text-gray-600\">{feature}</span>\n                    </motion.div>\n                  ))}\n                </div>\n              </div>\n              \n              {/* Floating Security Elements */}\n              <motion.div\n                animate={{ \n                  rotate: [0, 360],\n                  scale: [1, 1.1, 1]\n                }}\n                transition={{ \n                  duration: 8, \n                  repeat: Infinity,\n                  ease: \"linear\"\n                }}\n                className=\"absolute -top-4 -right-4 w-12 h-12 bg-green-500 rounded-full shadow-lg flex items-center justify-center\"\n              >\n                <Key className=\"w-6 h-6 text-white\" />\n              </motion.div>\n              \n              <motion.div\n                animate={{ \n                  y: [-10, 10, -10],\n                  rotate: [0, 180, 360]\n                }}\n                transition={{ \n                  duration: 6, \n                  repeat: Infinity,\n                  ease: \"easeInOut\"\n                }}\n                className=\"absolute -bottom-4 -left-4 w-8 h-8 bg-blue-500 rounded-full shadow-lg flex items-center justify-center\"\n              >\n                <Shield className=\"w-4 h-4 text-white\" />\n              </motion.div>\n            </div>\n          </motion.div>\n\n          {/* Right Content */}\n          <motion.div\n            initial={{ opacity: 0, x: 50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, delay: 0.2 }}\n            viewport={{ once: true }}\n            className=\"space-y-8\"\n          >\n            <div className=\"space-y-6\">\n              <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 leading-tight\">\n                100% your data\n              </h2>\n              <p className=\"text-lg text-gray-600 leading-relaxed\">\n                The app is open source and your notes are saved to an open format, so you'll always have \n                access to them. Uses End-To-End Encryption (E2EE) to secure your notes and ensure no-one \n                but yourself can access them.\n              </p>\n            </div>\n\n            <div className=\"space-y-6\">\n              {securityFeatures.map((feature, index) => (\n                <motion.div\n                  key={feature.title}\n                  initial={{ opacity: 0, y: 20 }}\n                  whileInView={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.5, delay: index * 0.1 }}\n                  viewport={{ once: true }}\n                  className=\"flex items-start space-x-4\"\n                >\n                  <div className=\"flex-shrink-0 w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center\">\n                    <feature.icon className=\"w-6 h-6 text-green-600\" />\n                  </div>\n                  <div>\n                    <h3 className=\"font-semibold text-gray-900 mb-2\">{feature.title}</h3>\n                    <p className=\"text-gray-600\">{feature.description}</p>\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n\n            <div className=\"bg-green-50 border border-green-200 rounded-lg p-6\">\n              <div className=\"flex items-start space-x-3\">\n                <Shield className=\"w-6 h-6 text-green-600 mt-1\" />\n                <div>\n                  <h4 className=\"font-semibold text-green-900 mb-2\">Security Guarantee</h4>\n                  <p className=\"text-green-700 text-sm\">\n                    We're so confident in our security that we offer a $10,000 bug bounty for anyone \n                    who can compromise our encryption.\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              className=\"btn-primary flex items-center space-x-2 group\"\n            >\n              <span>Read our security</span>\n              <ArrowRight className=\"w-5 h-5 group-hover:translate-x-1 transition-transform duration-200\" />\n            </motion.button>\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  )\n}\n\nexport default DataSecurity\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,eAAe;IACnB,MAAM,mBAAmB;QACvB;YACE,MAAM,sMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,kMAAA,CAAA,OAAI;YACV,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,gMAAA,CAAA,MAAG;YACT,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM,sMAAA,CAAA,SAAM;YACZ,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA8B;;;;;;8DAC5C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAK,WAAU;sEAAqC;;;;;;;;;;;;;;;;;;sDAKzD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,8OAAC;4DAAI,WAAU;sEAAoC;;;;;;sEACnD,8OAAC;4DAAI,WAAU;sEAAyB;;;;;;;;;;;;8DAG1C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;4DAAI,WAAU;sEAAmC;;;;;;sEAClD,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAK3C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAoC;;;;;;gDAClD;oDACC;oDACA;oDACA;oDACA;iDACD,CAAC,GAAG,CAAC,CAAC,SAAS,sBACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDAET,SAAS;4DAAE,SAAS;4DAAG,GAAG,CAAC;wDAAG;wDAC9B,aAAa;4DAAE,SAAS;4DAAG,GAAG;wDAAE;wDAChC,YAAY;4DAAE,UAAU;4DAAK,OAAO,QAAQ;wDAAI;wDAChD,UAAU;4DAAE,MAAM;wDAAK;wDACvB,WAAU;;0EAEV,8OAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;0EACvB,8OAAC;gEAAK,WAAU;0EAAyB;;;;;;;uDARpC;;;;;;;;;;;;;;;;;8CAeb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCACP,QAAQ;4CAAC;4CAAG;yCAAI;wCAChB,OAAO;4CAAC;4CAAG;4CAAK;yCAAE;oCACpB;oCACA,YAAY;wCACV,UAAU;wCACV,QAAQ;wCACR,MAAM;oCACR;oCACA,WAAU;8CAEV,cAAA,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;;;;;;8CAGjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCACP,GAAG;4CAAC,CAAC;4CAAI;4CAAI,CAAC;yCAAG;wCACjB,QAAQ;4CAAC;4CAAG;4CAAK;yCAAI;oCACvB;oCACA,YAAY;wCACV,UAAU;wCACV,QAAQ;wCACR,MAAM;oCACR;oCACA,WAAU;8CAEV,cAAA,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAMxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAyE;;;;;;kDAGvF,8OAAC;wCAAE,WAAU;kDAAwC;;;;;;;;;;;;0CAOvD,8OAAC;gCAAI,WAAU;0CACZ,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,aAAa;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO,QAAQ;wCAAI;wCAChD,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,QAAQ,IAAI;oDAAC,WAAU;;;;;;;;;;;0DAE1B,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAoC,QAAQ,KAAK;;;;;;kEAC/D,8OAAC;wDAAE,WAAU;kEAAiB,QAAQ,WAAW;;;;;;;;;;;;;uCAZ9C,QAAQ,KAAK;;;;;;;;;;0CAkBxB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAoC;;;;;;8DAClD,8OAAC;oDAAE,WAAU;8DAAyB;;;;;;;;;;;;;;;;;;;;;;;0CAQ5C,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAU;;kDAEV,8OAAC;kDAAK;;;;;;kDACN,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC;uCAEe", "debugId": null}}, {"offset": {"line": 4092, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/site%20web%20edara/src/components/Sponsors.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\n\nconst Sponsors = () => {\n  const sponsors = [\n    { name: 'Apple', logo: '🍎' },\n    { name: 'Microsoft', logo: '🪟' },\n    { name: 'Slack', logo: '💬' },\n    { name: 'Google', logo: '🔍' },\n  ]\n\n  return (\n    <section className=\"section-padding bg-white\">\n      <div className=\"container-custom\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center space-y-12\"\n        >\n          <h2 className=\"text-2xl md:text-3xl font-bold text-gray-900\">\n            Our sponsors\n          </h2>\n          \n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8 items-center\">\n            {sponsors.map((sponsor, index) => (\n              <motion.div\n                key={sponsor.name}\n                initial={{ opacity: 0, scale: 0.8 }}\n                whileInView={{ opacity: 1, scale: 1 }}\n                transition={{ duration: 0.5, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                whileHover={{ scale: 1.05 }}\n                className=\"flex flex-col items-center space-y-4 p-6 rounded-xl hover:bg-gray-50 transition-colors duration-200 cursor-pointer\"\n              >\n                <div className=\"text-4xl\">{sponsor.logo}</div>\n                <span className=\"text-lg font-semibold text-gray-700\">{sponsor.name}</span>\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  )\n}\n\nexport default Sponsors\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,MAAM,WAAW;IACf,MAAM,WAAW;QACf;YAAE,MAAM;YAAS,MAAM;QAAK;QAC5B;YAAE,MAAM;YAAa,MAAM;QAAK;QAChC;YAAE,MAAM;YAAS,MAAM;QAAK;QAC5B;YAAE,MAAM;YAAU,MAAM;QAAK;KAC9B;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,aAAa;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAChC,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,UAAU;oBAAE,MAAM;gBAAK;gBACvB,WAAU;;kCAEV,8OAAC;wBAAG,WAAU;kCAA+C;;;;;;kCAI7D,8OAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,aAAa;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCACpC,YAAY;oCAAE,UAAU;oCAAK,OAAO,QAAQ;gCAAI;gCAChD,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;kDAAY,QAAQ,IAAI;;;;;;kDACvC,8OAAC;wCAAK,WAAU;kDAAuC,QAAQ,IAAI;;;;;;;+BAT9D,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBjC;uCAEe", "debugId": null}}, {"offset": {"line": 4222, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/site%20web%20edara/src/components/AppIntegrations.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { ArrowRight, Plus } from 'lucide-react'\n\nconst AppIntegrations = () => {\n  const apps = [\n    { name: 'Google Drive', logo: '📁', color: 'bg-blue-100' },\n    { name: 'Dropbox', logo: '📦', color: 'bg-blue-200' },\n    { name: 'OneDrive', logo: '☁️', color: 'bg-blue-300' },\n    { name: 'Slack', logo: '💬', color: 'bg-purple-100' },\n    { name: '<PERSON>rell<PERSON>', logo: '📋', color: 'bg-blue-100' },\n    { name: 'Notion', logo: '📝', color: 'bg-gray-100' },\n    { name: 'Figma', logo: '🎨', color: 'bg-pink-100' },\n    { name: 'GitHub', logo: '🐙', color: 'bg-gray-200' },\n  ]\n\n  return (\n    <section className=\"section-padding bg-gray-50\">\n      <div className=\"container-custom\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center space-y-12\"\n        >\n          <div className=\"space-y-6\">\n            <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900\">\n              Work with Your Favorite Apps Using{' '}\n              <span className=\"text-blue-600\">whitespace</span>\n            </h2>\n            <p className=\"text-lg text-gray-600 max-w-3xl mx-auto\">\n              Whitespace teams up with your favorite software. Integrate with over 1000+ apps \n              with Zapier to have all the tools you need for your project success.\n            </p>\n          </div>\n\n          {/* Apps Grid */}\n          <div className=\"relative\">\n            {/* Central Whitespace Logo */}\n            <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-20\">\n              <motion.div\n                animate={{ rotate: [0, 360] }}\n                transition={{ duration: 20, repeat: Infinity, ease: \"linear\" }}\n                className=\"w-20 h-20 bg-blue-600 rounded-full shadow-xl flex items-center justify-center\"\n              >\n                <span className=\"text-white font-bold text-2xl\">W</span>\n              </motion.div>\n            </div>\n\n            {/* Apps arranged in a circle */}\n            <div className=\"relative w-96 h-96 mx-auto\">\n              {apps.map((app, index) => {\n                const angle = (index * 360) / apps.length\n                const radius = 140\n                const x = Math.cos((angle * Math.PI) / 180) * radius\n                const y = Math.sin((angle * Math.PI) / 180) * radius\n\n                return (\n                  <motion.div\n                    key={app.name}\n                    initial={{ opacity: 0, scale: 0 }}\n                    whileInView={{ opacity: 1, scale: 1 }}\n                    transition={{ duration: 0.5, delay: index * 0.1 }}\n                    viewport={{ once: true }}\n                    className=\"absolute\"\n                    style={{\n                      left: `calc(50% + ${x}px)`,\n                      top: `calc(50% + ${y}px)`,\n                      transform: 'translate(-50%, -50%)'\n                    }}\n                  >\n                    <motion.div\n                      whileHover={{ scale: 1.1 }}\n                      animate={{ \n                        y: [0, -10, 0],\n                        rotate: [0, 5, -5, 0]\n                      }}\n                      transition={{ \n                        duration: 3 + index * 0.2, \n                        repeat: Infinity,\n                        ease: \"easeInOut\"\n                      }}\n                      className={`w-16 h-16 ${app.color} rounded-xl shadow-lg flex flex-col items-center justify-center cursor-pointer hover:shadow-xl transition-shadow duration-200`}\n                    >\n                      <div className=\"text-2xl mb-1\">{app.logo}</div>\n                      <div className=\"text-xs font-medium text-gray-700 text-center leading-tight\">\n                        {app.name}\n                      </div>\n                    </motion.div>\n\n                    {/* Connection Line */}\n                    <svg \n                      className=\"absolute top-8 left-8 pointer-events-none\"\n                      style={{\n                        width: '200px',\n                        height: '200px',\n                        transform: 'translate(-50%, -50%)'\n                      }}\n                    >\n                      <motion.line\n                        initial={{ pathLength: 0, opacity: 0 }}\n                        whileInView={{ pathLength: 1, opacity: 0.2 }}\n                        transition={{ duration: 1, delay: index * 0.1 }}\n                        viewport={{ once: true }}\n                        x1=\"0\"\n                        y1=\"0\"\n                        x2={-x * 0.7}\n                        y2={-y * 0.7}\n                        stroke=\"#3B82F6\"\n                        strokeWidth=\"2\"\n                        strokeDasharray=\"5,5\"\n                      />\n                    </svg>\n                  </motion.div>\n                )\n              })}\n            </div>\n\n            {/* Plus Icons for more integrations */}\n            <motion.div\n              animate={{ rotate: [0, 360] }}\n              transition={{ duration: 15, repeat: Infinity, ease: \"linear\" }}\n              className=\"absolute top-4 right-4 w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center\"\n            >\n              <Plus className=\"w-4 h-4 text-gray-600\" />\n            </motion.div>\n            \n            <motion.div\n              animate={{ rotate: [360, 0] }}\n              transition={{ duration: 12, repeat: Infinity, ease: \"linear\" }}\n              className=\"absolute bottom-4 left-4 w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center\"\n            >\n              <Plus className=\"w-4 h-4 text-gray-600\" />\n            </motion.div>\n          </div>\n\n          {/* CTA */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.5 }}\n            viewport={{ once: true }}\n            className=\"space-y-6\"\n          >\n            <p className=\"text-gray-600\">\n              Connect with 1000+ apps through Zapier, or use our API to build custom integrations.\n            </p>\n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              className=\"btn-primary flex items-center space-x-2 group mx-auto\"\n            >\n              <span>View all integrations</span>\n              <ArrowRight className=\"w-5 h-5 group-hover:translate-x-1 transition-transform duration-200\" />\n            </motion.button>\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  )\n}\n\nexport default AppIntegrations\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAKA,MAAM,kBAAkB;IACtB,MAAM,OAAO;QACX;YAAE,MAAM;YAAgB,MAAM;YAAM,OAAO;QAAc;QACzD;YAAE,MAAM;YAAW,MAAM;YAAM,OAAO;QAAc;QACpD;YAAE,MAAM;YAAY,MAAM;YAAM,OAAO;QAAc;QACrD;YAAE,MAAM;YAAS,MAAM;YAAM,OAAO;QAAgB;QACpD;YAAE,MAAM;YAAU,MAAM;YAAM,OAAO;QAAc;QACnD;YAAE,MAAM;YAAU,MAAM;YAAM,OAAO;QAAc;QACnD;YAAE,MAAM;YAAS,MAAM;YAAM,OAAO;QAAc;QAClD;YAAE,MAAM;YAAU,MAAM;YAAM,OAAO;QAAc;KACpD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,aAAa;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAChC,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,UAAU;oBAAE,MAAM;gBAAK;gBACvB,WAAU;;kCAEV,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAA2D;oCACpC;kDACnC,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAElC,8OAAC;gCAAE,WAAU;0CAA0C;;;;;;;;;;;;kCAOzD,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,QAAQ;4CAAC;4CAAG;yCAAI;oCAAC;oCAC5B,YAAY;wCAAE,UAAU;wCAAI,QAAQ;wCAAU,MAAM;oCAAS;oCAC7D,WAAU;8CAEV,cAAA,8OAAC;wCAAK,WAAU;kDAAgC;;;;;;;;;;;;;;;;0CAKpD,8OAAC;gCAAI,WAAU;0CACZ,KAAK,GAAG,CAAC,CAAC,KAAK;oCACd,MAAM,QAAQ,AAAC,QAAQ,MAAO,KAAK,MAAM;oCACzC,MAAM,SAAS;oCACf,MAAM,IAAI,KAAK,GAAG,CAAC,AAAC,QAAQ,KAAK,EAAE,GAAI,OAAO;oCAC9C,MAAM,IAAI,KAAK,GAAG,CAAC,AAAC,QAAQ,KAAK,EAAE,GAAI,OAAO;oCAE9C,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCAChC,aAAa;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCACpC,YAAY;4CAAE,UAAU;4CAAK,OAAO,QAAQ;wCAAI;wCAChD,UAAU;4CAAE,MAAM;wCAAK;wCACvB,WAAU;wCACV,OAAO;4CACL,MAAM,CAAC,WAAW,EAAE,EAAE,GAAG,CAAC;4CAC1B,KAAK,CAAC,WAAW,EAAE,EAAE,GAAG,CAAC;4CACzB,WAAW;wCACb;;0DAEA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,YAAY;oDAAE,OAAO;gDAAI;gDACzB,SAAS;oDACP,GAAG;wDAAC;wDAAG,CAAC;wDAAI;qDAAE;oDACd,QAAQ;wDAAC;wDAAG;wDAAG,CAAC;wDAAG;qDAAE;gDACvB;gDACA,YAAY;oDACV,UAAU,IAAI,QAAQ;oDACtB,QAAQ;oDACR,MAAM;gDACR;gDACA,WAAW,CAAC,UAAU,EAAE,IAAI,KAAK,CAAC,6HAA6H,CAAC;;kEAEhK,8OAAC;wDAAI,WAAU;kEAAiB,IAAI,IAAI;;;;;;kEACxC,8OAAC;wDAAI,WAAU;kEACZ,IAAI,IAAI;;;;;;;;;;;;0DAKb,8OAAC;gDACC,WAAU;gDACV,OAAO;oDACL,OAAO;oDACP,QAAQ;oDACR,WAAW;gDACb;0DAEA,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oDACV,SAAS;wDAAE,YAAY;wDAAG,SAAS;oDAAE;oDACrC,aAAa;wDAAE,YAAY;wDAAG,SAAS;oDAAI;oDAC3C,YAAY;wDAAE,UAAU;wDAAG,OAAO,QAAQ;oDAAI;oDAC9C,UAAU;wDAAE,MAAM;oDAAK;oDACvB,IAAG;oDACH,IAAG;oDACH,IAAI,CAAC,IAAI;oDACT,IAAI,CAAC,IAAI;oDACT,QAAO;oDACP,aAAY;oDACZ,iBAAgB;;;;;;;;;;;;uCAnDf,IAAI,IAAI;;;;;gCAwDnB;;;;;;0CAIF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,QAAQ;wCAAC;wCAAG;qCAAI;gCAAC;gCAC5B,YAAY;oCAAE,UAAU;oCAAI,QAAQ;oCAAU,MAAM;gCAAS;gCAC7D,WAAU;0CAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAGlB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,QAAQ;wCAAC;wCAAK;qCAAE;gCAAC;gCAC5B,YAAY;oCAAE,UAAU;oCAAI,QAAQ;oCAAU,MAAM;gCAAS;gCAC7D,WAAU;0CAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAKpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;wBACxC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,WAAU;;0CAEV,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;0CAG7B,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAU;;kDAEV,8OAAC;kDAAK;;;;;;kDACN,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC;uCAEe", "debugId": null}}, {"offset": {"line": 4643, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/site%20web%20edara/src/components/Testimonials.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { Star, Quote } from 'lucide-react'\nimport testimonialsData from '@/data/testimonials.json'\n\nconst Testimonials = () => {\n  return (\n    <section className=\"section-padding bg-gray-50\">\n      <div className=\"container-custom\">\n        {/* Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center space-y-6 mb-16\"\n        >\n          <h2 className=\"text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900\">\n            What Our Clients Say\n          </h2>\n          <p className=\"text-lg text-gray-600 max-w-2xl mx-auto\">\n            Don't just take our word for it. Here's what our customers have to say about their experience with Whitespace.\n          </p>\n        </motion.div>\n\n        {/* Testimonials Grid */}\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {testimonialsData.testimonials.map((testimonial, index) => (\n            <motion.div\n              key={testimonial.id}\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.5, delay: index * 0.1 }}\n              viewport={{ once: true }}\n              className=\"bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow duration-300 relative\"\n            >\n              {/* Quote Icon */}\n              <div className=\"absolute -top-4 left-8\">\n                <div className=\"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center\">\n                  <Quote className=\"w-4 h-4 text-white\" />\n                </div>\n              </div>\n\n              <div className=\"space-y-6\">\n                {/* Rating */}\n                <div className=\"flex items-center space-x-1\">\n                  {[...Array(testimonial.rating)].map((_, i) => (\n                    <Star key={i} className=\"w-5 h-5 text-yellow-400 fill-current\" />\n                  ))}\n                </div>\n\n                {/* Content */}\n                <blockquote className=\"text-gray-700 leading-relaxed\">\n                  \"{testimonial.content}\"\n                </blockquote>\n\n                {/* Author */}\n                <div className=\"flex items-center space-x-4\">\n                  <div className=\"w-12 h-12 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center\">\n                    <span className=\"text-white font-semibold text-lg\">\n                      {testimonial.name.split(' ').map(n => n[0]).join('')}\n                    </span>\n                  </div>\n                  <div>\n                    <div className=\"font-semibold text-gray-900\">{testimonial.name}</div>\n                    <div className=\"text-sm text-gray-600\">{testimonial.role}</div>\n                    <div className=\"text-sm text-blue-600\">{testimonial.company}</div>\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Bottom CTA */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.3 }}\n          viewport={{ once: true }}\n          className=\"text-center mt-16\"\n        >\n          <p className=\"text-gray-600 mb-6\">\n            Join thousands of satisfied customers who trust Whitespace for their productivity needs.\n          </p>\n          <motion.button\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n            className=\"btn-primary\"\n          >\n            Start Your Free Trial\n          </motion.button>\n        </motion.div>\n      </div>\n    </section>\n  )\n}\n\nexport default Testimonials\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAJA;;;;;AAMA,MAAM,eAAe;IACnB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAA2D;;;;;;sCAGzE,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,8OAAC;oBAAI,WAAU;8BACZ,mGAAA,CAAA,UAAgB,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,aAAa,sBAC/C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAGV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAIrB,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;sDACZ;mDAAI,MAAM,YAAY,MAAM;6CAAE,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtC,8OAAC,kMAAA,CAAA,OAAI;oDAAS,WAAU;mDAAb;;;;;;;;;;sDAKf,8OAAC;4CAAW,WAAU;;gDAAgC;gDAClD,YAAY,OAAO;gDAAC;;;;;;;sDAIxB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEACb,YAAY,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;8DAGrD,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEAA+B,YAAY,IAAI;;;;;;sEAC9D,8OAAC;4DAAI,WAAU;sEAAyB,YAAY,IAAI;;;;;;sEACxD,8OAAC;4DAAI,WAAU;sEAAyB,YAAY,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;2BArC5D,YAAY,EAAE;;;;;;;;;;8BA8CzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;4BACxB,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX;uCAEe", "debugId": null}}, {"offset": {"line": 4911, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/site%20web%20edara/src/components/FinalCTA.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { ArrowR<PERSON>, Spark<PERSON> } from 'lucide-react'\n\nconst FinalCTA = () => {\n  return (\n    <section className=\"section-padding bg-gradient-to-br from-blue-600 to-indigo-700 text-white relative overflow-hidden\">\n      {/* Background Elements */}\n      <div className=\"absolute inset-0\">\n        <motion.div\n          animate={{ \n            rotate: [0, 360],\n            scale: [1, 1.2, 1]\n          }}\n          transition={{ \n            duration: 20, \n            repeat: Infinity,\n            ease: \"linear\"\n          }}\n          className=\"absolute -top-24 -left-24 w-48 h-48 bg-white/10 rounded-full\"\n        ></motion.div>\n        \n        <motion.div\n          animate={{ \n            rotate: [360, 0],\n            scale: [1, 1.1, 1]\n          }}\n          transition={{ \n            duration: 15, \n            repeat: Infinity,\n            ease: \"linear\"\n          }}\n          className=\"absolute -bottom-16 -right-16 w-32 h-32 bg-white/10 rounded-full\"\n        ></motion.div>\n        \n        <motion.div\n          animate={{ \n            y: [-20, 20, -20],\n            x: [-10, 10, -10]\n          }}\n          transition={{ \n            duration: 8, \n            repeat: Infinity,\n            ease: \"easeInOut\"\n          }}\n          className=\"absolute top-1/4 right-1/4 w-16 h-16 bg-yellow-400/20 rounded-full\"\n        ></motion.div>\n      </div>\n\n      <div className=\"container-custom relative z-10\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center space-y-8 max-w-4xl mx-auto\"\n        >\n          {/* Icon */}\n          <motion.div\n            initial={{ opacity: 0, scale: 0 }}\n            whileInView={{ opacity: 1, scale: 1 }}\n            transition={{ duration: 0.5, delay: 0.2 }}\n            viewport={{ once: true }}\n            className=\"flex justify-center\"\n          >\n            <div className=\"w-16 h-16 bg-white/20 rounded-full flex items-center justify-center\">\n              <Sparkles className=\"w-8 h-8 text-yellow-400\" />\n            </div>\n          </motion.div>\n\n          {/* Heading */}\n          <div className=\"space-y-4\">\n            <motion.h2\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.3 }}\n              viewport={{ once: true }}\n              className=\"text-3xl md:text-4xl lg:text-5xl font-bold leading-tight\"\n            >\n              Try Whitespace today\n            </motion.h2>\n            \n            <motion.p\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.8, delay: 0.4 }}\n              viewport={{ once: true }}\n              className=\"text-lg md:text-xl text-blue-100 leading-relaxed\"\n            >\n              Get started for free. Add your whole team as your needs grow.\n            </motion.p>\n          </div>\n\n          {/* CTA Button */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.5 }}\n            viewport={{ once: true }}\n            className=\"flex justify-center\"\n          >\n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              className=\"bg-white text-blue-600 font-semibold px-8 py-4 rounded-lg hover:bg-gray-50 transition-colors duration-200 flex items-center space-x-2 group shadow-lg\"\n            >\n              <span>Start today</span>\n              <ArrowRight className=\"w-5 h-5 group-hover:translate-x-1 transition-transform duration-200\" />\n            </motion.button>\n          </motion.div>\n\n          {/* Additional Info */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.8, delay: 0.6 }}\n            viewport={{ once: true }}\n            className=\"space-y-4\"\n          >\n            <div className=\"flex flex-col sm:flex-row justify-center items-center space-y-2 sm:space-y-0 sm:space-x-8 text-blue-100\">\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-2 h-2 bg-green-400 rounded-full\"></div>\n                <span>Free 30-day trial</span>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-2 h-2 bg-green-400 rounded-full\"></div>\n                <span>No credit card required</span>\n              </div>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-2 h-2 bg-green-400 rounded-full\"></div>\n                <span>Cancel anytime</span>\n              </div>\n            </div>\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  )\n}\n\nexport default FinalCTA\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAKA,MAAM,WAAW;IACf,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BACP,QAAQ;gCAAC;gCAAG;6BAAI;4BAChB,OAAO;gCAAC;gCAAG;gCAAK;6BAAE;wBACpB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;wBACA,WAAU;;;;;;kCAGZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BACP,QAAQ;gCAAC;gCAAK;6BAAE;4BAChB,OAAO;gCAAC;gCAAG;gCAAK;6BAAE;wBACpB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;wBACA,WAAU;;;;;;kCAGZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BACP,GAAG;gCAAC,CAAC;gCAAI;gCAAI,CAAC;6BAAG;4BACjB,GAAG;gCAAC,CAAC;gCAAI;gCAAI,CAAC;6BAAG;wBACnB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;wBACA,WAAU;;;;;;;;;;;;0BAId,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAGV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BAChC,aAAa;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BACpC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;sCAKxB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oCACR,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;8CACX;;;;;;8CAID,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;8CACX;;;;;;;;;;;;sCAMH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;sCAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;gCACxB,WAAU;;kDAEV,8OAAC;kDAAK;;;;;;kDACN,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAK1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;sCAEV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtB;uCAEe", "debugId": null}}, {"offset": {"line": 5291, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/site%20web%20edara/src/components/Footer.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { Facebook, Twitter, Instagram, Linkedin, Mail, Phone, MapPin } from 'lucide-react'\nimport Link from 'next/link'\n\nconst Footer = () => {\n  const footerLinks = {\n    product: [\n      { name: 'Overview', href: '#' },\n      { name: 'Pricing', href: '#pricing' },\n      { name: 'Customer stories', href: '#' },\n    ],\n    resources: [\n      { name: 'Blog', href: '#' },\n      { name: 'Guides & tutorials', href: '#' },\n      { name: 'Help center', href: '#' },\n    ],\n    company: [\n      { name: 'About us', href: '#' },\n      { name: 'Careers', href: '#' },\n      { name: 'Media kit', href: '#' },\n    ]\n  }\n\n  const socialLinks = [\n    { icon: Facebook, href: '#', label: 'Facebook' },\n    { icon: Twitter, href: '#', label: 'Twitter' },\n    { icon: Instagram, href: '#', label: 'Instagram' },\n    { icon: Linkedin, href: '#', label: 'LinkedIn' },\n  ]\n\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      {/* Main Footer */}\n      <div className=\"container-custom py-16\">\n        <div className=\"grid lg:grid-cols-4 md:grid-cols-2 gap-8\">\n          {/* Company Info */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5 }}\n            viewport={{ once: true }}\n            className=\"space-y-6\"\n          >\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-lg\">W</span>\n              </div>\n              <span className=\"text-xl font-bold\">whitespace</span>\n            </Link>\n            \n            <p className=\"text-gray-400 leading-relaxed\">\n              whitespace was created for the new ways we live and work. \n              We make a better workspace around the world.\n            </p>\n\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center space-x-3 text-gray-400\">\n                <Mail className=\"w-5 h-5\" />\n                <span><EMAIL></span>\n              </div>\n              <div className=\"flex items-center space-x-3 text-gray-400\">\n                <Phone className=\"w-5 h-5\" />\n                <span>+****************</span>\n              </div>\n              <div className=\"flex items-center space-x-3 text-gray-400\">\n                <MapPin className=\"w-5 h-5\" />\n                <span>123 Business Ave, Suite 100</span>\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Product Links */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5, delay: 0.1 }}\n            viewport={{ once: true }}\n            className=\"space-y-6\"\n          >\n            <h3 className=\"text-lg font-semibold\">Product</h3>\n            <ul className=\"space-y-3\">\n              {footerLinks.product.map((link) => (\n                <li key={link.name}>\n                  <Link \n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white transition-colors duration-200\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </motion.div>\n\n          {/* Resources Links */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5, delay: 0.2 }}\n            viewport={{ once: true }}\n            className=\"space-y-6\"\n          >\n            <h3 className=\"text-lg font-semibold\">Resources</h3>\n            <ul className=\"space-y-3\">\n              {footerLinks.resources.map((link) => (\n                <li key={link.name}>\n                  <Link \n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white transition-colors duration-200\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </motion.div>\n\n          {/* Company Links */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.5, delay: 0.3 }}\n            viewport={{ once: true }}\n            className=\"space-y-6\"\n          >\n            <h3 className=\"text-lg font-semibold\">Company</h3>\n            <ul className=\"space-y-3\">\n              {footerLinks.company.map((link) => (\n                <li key={link.name}>\n                  <Link \n                    href={link.href}\n                    className=\"text-gray-400 hover:text-white transition-colors duration-200\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n\n            {/* Social Links */}\n            <div className=\"pt-4\">\n              <h4 className=\"text-sm font-medium mb-4\">Follow us</h4>\n              <div className=\"flex space-x-4\">\n                {socialLinks.map((social) => (\n                  <motion.a\n                    key={social.label}\n                    href={social.href}\n                    whileHover={{ scale: 1.1 }}\n                    whileTap={{ scale: 0.9 }}\n                    className=\"w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center text-gray-400 hover:text-white hover:bg-blue-600 transition-colors duration-200\"\n                    aria-label={social.label}\n                  >\n                    <social.icon className=\"w-5 h-5\" />\n                  </motion.a>\n                ))}\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n\n      {/* Bottom Bar */}\n      <div className=\"border-t border-gray-800\">\n        <div className=\"container-custom py-6\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            <div className=\"text-gray-400 text-sm\">\n              © 2024 Whitespace LLC. All rights reserved.\n            </div>\n            <div className=\"flex space-x-6 text-sm\">\n              <Link href=\"#\" className=\"text-gray-400 hover:text-white transition-colors duration-200\">\n                Privacy Policy\n              </Link>\n              <Link href=\"#\" className=\"text-gray-400 hover:text-white transition-colors duration-200\">\n                Terms of Service\n              </Link>\n              <Link href=\"#\" className=\"text-gray-400 hover:text-white transition-colors duration-200\">\n                Cookie Policy\n              </Link>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  )\n}\n\nexport default Footer\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAMA,MAAM,SAAS;IACb,MAAM,cAAc;QAClB,SAAS;YACP;gBAAE,MAAM;gBAAY,MAAM;YAAI;YAC9B;gBAAE,MAAM;gBAAW,MAAM;YAAW;YACpC;gBAAE,MAAM;gBAAoB,MAAM;YAAI;SACvC;QACD,WAAW;YACT;gBAAE,MAAM;gBAAQ,MAAM;YAAI;YAC1B;gBAAE,MAAM;gBAAsB,MAAM;YAAI;YACxC;gBAAE,MAAM;gBAAe,MAAM;YAAI;SAClC;QACD,SAAS;YACP;gBAAE,MAAM;gBAAY,MAAM;YAAI;YAC9B;gBAAE,MAAM;gBAAW,MAAM;YAAI;YAC7B;gBAAE,MAAM;gBAAa,MAAM;YAAI;SAChC;IACH;IAEA,MAAM,cAAc;QAClB;YAAE,MAAM,0MAAA,CAAA,WAAQ;YAAE,MAAM;YAAK,OAAO;QAAW;QAC/C;YAAE,MAAM,wMAAA,CAAA,UAAO;YAAE,MAAM;YAAK,OAAO;QAAU;QAC7C;YAAE,MAAM,4MAAA,CAAA,YAAS;YAAE,MAAM;YAAK,OAAO;QAAY;QACjD;YAAE,MAAM,0MAAA,CAAA,WAAQ;YAAE,MAAM;YAAK,OAAO;QAAW;KAChD;IAED,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,8OAAC;4CAAK,WAAU;sDAAoB;;;;;;;;;;;;8CAGtC,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;8CAK7C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;sCAMZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC;oCAAG,WAAU;8CAAwB;;;;;;8CACtC,8OAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC;oCAAG,WAAU;8CAAwB;;;;;;8CACtC,8OAAC;oCAAG,WAAU;8CACX,YAAY,SAAS,CAAC,GAAG,CAAC,CAAC,qBAC1B,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC;oCAAG,WAAU;8CAAwB;;;;;;8CACtC,8OAAC;oCAAG,WAAU;8CACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;8CAYtB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2B;;;;;;sDACzC,8OAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oDAEP,MAAM,OAAO,IAAI;oDACjB,YAAY;wDAAE,OAAO;oDAAI;oDACzB,UAAU;wDAAE,OAAO;oDAAI;oDACvB,WAAU;oDACV,cAAY,OAAO,KAAK;8DAExB,cAAA,8OAAC,OAAO,IAAI;wDAAC,WAAU;;;;;;mDAPlB,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAiB/B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAwB;;;;;;0CAGvC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDAAgE;;;;;;kDAGzF,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDAAgE;;;;;;kDAGzF,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDAAgE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASvG;uCAEe", "debugId": null}}]}