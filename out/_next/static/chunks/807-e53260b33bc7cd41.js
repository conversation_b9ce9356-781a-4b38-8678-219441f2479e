"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[807],{224:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(9946).A)("quote",[["path",{d:"M16 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"rib7q0"}],["path",{d:"M5 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"1ymkrd"}]])},381:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},488:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(9946).A)("facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]])},646:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1539:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(9946).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},1788:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(9946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},1934:(t,e,i)=>{let r;function n(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function s(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function o(t,e,i,r){if("function"==typeof e){let[n,o]=s(r);e=e(void 0!==i?i:t.custom,n,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[n,o]=s(r);e=e(void 0!==i?i:t.custom,n,o)}return e}function a(t,e,i){let r=t.getProps();return o(r,e,void 0!==i?i:r.custom,t)}function l(t,e){return t?.[e]??t?.default??t}i.d(e,{P:()=>sP});let u=t=>t,h={},d=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],c={value:null,addProjectionMetrics:null};function p(t,e){let i=!1,r=!0,n={delta:0,timestamp:0,isProcessing:!1},s=()=>i=!0,o=d.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,r=new Set,n=!1,s=!1,o=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){o.has(e)&&(h.schedule(e),t()),l++,e(a)}let h={schedule:(t,e=!1,s=!1)=>{let a=s&&n?i:r;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{r.delete(t),o.delete(t)},process:t=>{if(a=t,n){s=!0;return}n=!0,[i,r]=[r,i],i.forEach(u),e&&c.value&&c.value.frameloop[e].push(l),l=0,i.clear(),n=!1,s&&(s=!1,h.process(t))}};return h}(s,e?i:void 0),t),{}),{setup:a,read:l,resolveKeyframes:u,preUpdate:p,update:m,preRender:f,render:y,postRender:g}=o,v=()=>{let s=h.useManualTiming?n.timestamp:performance.now();i=!1,h.useManualTiming||(n.delta=r?1e3/60:Math.max(Math.min(s-n.timestamp,40),1)),n.timestamp=s,n.isProcessing=!0,a.process(n),l.process(n),u.process(n),p.process(n),m.process(n),f.process(n),y.process(n),g.process(n),n.isProcessing=!1,i&&e&&(r=!1,t(v))},x=()=>{i=!0,r=!0,n.isProcessing||t(v)};return{schedule:d.reduce((t,e)=>{let r=o[e];return t[e]=(t,e=!1,n=!1)=>(i||x(),r.schedule(t,e,n)),t},{}),cancel:t=>{for(let e=0;e<d.length;e++)o[d[e]].cancel(t)},state:n,steps:o}}let{schedule:m,cancel:f,state:y,steps:g}=p("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:u,!0),v=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],x=new Set(v),b=new Set(["width","height","top","left","right","bottom",...v]);function w(t,e){-1===t.indexOf(e)&&t.push(e)}function k(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class A{constructor(){this.subscriptions=[]}add(t){return w(this.subscriptions,t),()=>k(this.subscriptions,t)}notify(t,e,i){let r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](t,e,i);else for(let n=0;n<r;n++){let r=this.subscriptions[n];r&&r(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function T(){r=void 0}let P={now:()=>(void 0===r&&P.set(y.isProcessing||h.useManualTiming?y.timestamp:performance.now()),r),set:t=>{r=t,queueMicrotask(T)}},M=t=>!isNaN(parseFloat(t)),S={current:void 0};class E{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=P.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=P.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=M(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new A);let i=this.events[t].add(e);return"change"===t?()=>{i(),m.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return S.current&&S.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=P.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function V(t,e){return new E(t,e)}let C=t=>Array.isArray(t),D=t=>!!(t&&t.getVelocity);function j(t,e){let i=t.getValue("willChange");if(D(i)&&i.add)return i.add(e);if(!i&&h.WillChange){let i=new h.WillChange("auto");t.addValue("willChange",i),i.add(e)}}let R=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),L="data-"+R("framerAppearId"),F=(t,e)=>i=>e(t(i)),O=(...t)=>t.reduce(F),z=(t,e,i)=>i>e?e:i<t?t:i,B=t=>1e3*t,I=t=>t/1e3,U={layout:0,mainThread:0,waapi:0},N=()=>{},$=()=>{},W=t=>e=>"string"==typeof e&&e.startsWith(t),_=W("--"),q=W("var(--"),Y=t=>!!q(t)&&H.test(t.split("/*")[0].trim()),H=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,K={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},X={...K,transform:t=>z(0,1,t)},G={...K,default:1},Z=t=>Math.round(1e5*t)/1e5,Q=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,J=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tt=(t,e)=>i=>!!("string"==typeof i&&J.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),te=(t,e,i)=>r=>{if("string"!=typeof r)return r;let[n,s,o,a]=r.match(Q);return{[t]:parseFloat(n),[e]:parseFloat(s),[i]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},ti=t=>z(0,255,t),tr={...K,transform:t=>Math.round(ti(t))},tn={test:tt("rgb","red"),parse:te("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:r=1})=>"rgba("+tr.transform(t)+", "+tr.transform(e)+", "+tr.transform(i)+", "+Z(X.transform(r))+")"},ts={test:tt("#"),parse:function(t){let e="",i="",r="",n="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),r=t.substring(5,7),n=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),r=t.substring(3,4),n=t.substring(4,5),e+=e,i+=i,r+=r,n+=n),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(r,16),alpha:n?parseInt(n,16)/255:1}},transform:tn.transform},to=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),ta=to("deg"),tl=to("%"),tu=to("px"),th=to("vh"),td=to("vw"),tc={...tl,parse:t=>tl.parse(t)/100,transform:t=>tl.transform(100*t)},tp={test:tt("hsl","hue"),parse:te("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:r=1})=>"hsla("+Math.round(t)+", "+tl.transform(Z(e))+", "+tl.transform(Z(i))+", "+Z(X.transform(r))+")"},tm={test:t=>tn.test(t)||ts.test(t)||tp.test(t),parse:t=>tn.test(t)?tn.parse(t):tp.test(t)?tp.parse(t):ts.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?tn.transform(t):tp.transform(t)},tf=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,ty="number",tg="color",tv=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tx(t){let e=t.toString(),i=[],r={color:[],number:[],var:[]},n=[],s=0,o=e.replace(tv,t=>(tm.test(t)?(r.color.push(s),n.push(tg),i.push(tm.parse(t))):t.startsWith("var(")?(r.var.push(s),n.push("var"),i.push(t)):(r.number.push(s),n.push(ty),i.push(parseFloat(t))),++s,"${}")).split("${}");return{values:i,split:o,indexes:r,types:n}}function tb(t){return tx(t).values}function tw(t){let{split:e,types:i}=tx(t),r=e.length;return t=>{let n="";for(let s=0;s<r;s++)if(n+=e[s],void 0!==t[s]){let e=i[s];e===ty?n+=Z(t[s]):e===tg?n+=tm.transform(t[s]):n+=t[s]}return n}}let tk=t=>"number"==typeof t?0:t,tA={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(Q)?.length||0)+(t.match(tf)?.length||0)>0},parse:tb,createTransformer:tw,getAnimatableNone:function(t){let e=tb(t);return tw(t)(e.map(tk))}};function tT(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function tP(t,e){return i=>i>0?e:t}let tM=(t,e,i)=>t+(e-t)*i,tS=(t,e,i)=>{let r=t*t,n=i*(e*e-r)+r;return n<0?0:Math.sqrt(n)},tE=[ts,tn,tp],tV=t=>tE.find(e=>e.test(t));function tC(t){let e=tV(t);if(N(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===tp&&(i=function({hue:t,saturation:e,lightness:i,alpha:r}){t/=360,i/=100;let n=0,s=0,o=0;if(e/=100){let r=i<.5?i*(1+e):i+e-i*e,a=2*i-r;n=tT(a,r,t+1/3),s=tT(a,r,t),o=tT(a,r,t-1/3)}else n=s=o=i;return{red:Math.round(255*n),green:Math.round(255*s),blue:Math.round(255*o),alpha:r}}(i)),i}let tD=(t,e)=>{let i=tC(t),r=tC(e);if(!i||!r)return tP(t,e);let n={...i};return t=>(n.red=tS(i.red,r.red,t),n.green=tS(i.green,r.green,t),n.blue=tS(i.blue,r.blue,t),n.alpha=tM(i.alpha,r.alpha,t),tn.transform(n))},tj=new Set(["none","hidden"]);function tR(t,e){return i=>tM(t,e,i)}function tL(t){return"number"==typeof t?tR:"string"==typeof t?Y(t)?tP:tm.test(t)?tD:tz:Array.isArray(t)?tF:"object"==typeof t?tm.test(t)?tD:tO:tP}function tF(t,e){let i=[...t],r=i.length,n=t.map((t,i)=>tL(t)(t,e[i]));return t=>{for(let e=0;e<r;e++)i[e]=n[e](t);return i}}function tO(t,e){let i={...t,...e},r={};for(let n in i)void 0!==t[n]&&void 0!==e[n]&&(r[n]=tL(t[n])(t[n],e[n]));return t=>{for(let e in r)i[e]=r[e](t);return i}}let tz=(t,e)=>{let i=tA.createTransformer(e),r=tx(t),n=tx(e);return r.indexes.var.length===n.indexes.var.length&&r.indexes.color.length===n.indexes.color.length&&r.indexes.number.length>=n.indexes.number.length?tj.has(t)&&!n.values.length||tj.has(e)&&!r.values.length?function(t,e){return tj.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):O(tF(function(t,e){let i=[],r={color:0,var:0,number:0};for(let n=0;n<e.values.length;n++){let s=e.types[n],o=t.indexes[s][r[s]],a=t.values[o]??0;i[n]=a,r[s]++}return i}(r,n),n.values),i):(N(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),tP(t,e))};function tB(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?tM(t,e,i):tL(t)(t,e)}let tI=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>m.update(e,t),stop:()=>f(e),now:()=>y.isProcessing?y.timestamp:P.now()}},tU=(t,e,i=10)=>{let r="",n=Math.max(Math.round(e/i),2);for(let e=0;e<n;e++)r+=t(e/(n-1))+", ";return`linear(${r.substring(0,r.length-2)})`};function tN(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function t$(t,e,i){var r,n;let s=Math.max(e-5,0);return r=i-t(s),(n=e-s)?1e3/n*r:0}let tW={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function t_(t,e){return t*Math.sqrt(1-e*e)}let tq=["duration","bounce"],tY=["stiffness","damping","mass"];function tH(t,e){return e.some(e=>void 0!==t[e])}function tK(t=tW.visualDuration,e=tW.bounce){let i,r="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:n,restDelta:s}=r,o=r.keyframes[0],a=r.keyframes[r.keyframes.length-1],l={done:!1,value:o},{stiffness:u,damping:h,mass:d,duration:c,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:tW.velocity,stiffness:tW.stiffness,damping:tW.damping,mass:tW.mass,isResolvedFromDuration:!1,...t};if(!tH(t,tY)&&tH(t,tq))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),r=i*i,n=2*z(.05,1,1-(t.bounce||0))*Math.sqrt(r);e={...e,mass:tW.mass,stiffness:r,damping:n}}else{let i=function({duration:t=tW.duration,bounce:e=tW.bounce,velocity:i=tW.velocity,mass:r=tW.mass}){let n,s;N(t<=B(tW.maxDuration),"Spring duration must be 10 seconds or less");let o=1-e;o=z(tW.minDamping,tW.maxDamping,o),t=z(tW.minDuration,tW.maxDuration,I(t)),o<1?(n=e=>{let r=e*o,n=r*t;return .001-(r-i)/t_(e,o)*Math.exp(-n)},s=e=>{let r=e*o*t,s=Math.pow(o,2)*Math.pow(e,2)*t,a=Math.exp(-r),l=t_(Math.pow(e,2),o);return(r*i+i-s)*a*(-n(e)+.001>0?-1:1)/l}):(n=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),s=e=>t*t*(i-e)*Math.exp(-e*t));let a=function(t,e,i){let r=i;for(let i=1;i<12;i++)r-=t(r)/e(r);return r}(n,s,5/t);if(t=B(t),isNaN(a))return{stiffness:tW.stiffness,damping:tW.damping,duration:t};{let e=Math.pow(a,2)*r;return{stiffness:e,damping:2*o*Math.sqrt(r*e),duration:t}}}(t);(e={...e,...i,mass:tW.mass}).isResolvedFromDuration=!0}return e}({...r,velocity:-I(r.velocity||0)}),f=p||0,y=h/(2*Math.sqrt(u*d)),g=a-o,v=I(Math.sqrt(u/d)),x=5>Math.abs(g);if(n||(n=x?tW.restSpeed.granular:tW.restSpeed.default),s||(s=x?tW.restDelta.granular:tW.restDelta.default),y<1){let t=t_(v,y);i=e=>a-Math.exp(-y*v*e)*((f+y*v*g)/t*Math.sin(t*e)+g*Math.cos(t*e))}else if(1===y)i=t=>a-Math.exp(-v*t)*(g+(f+v*g)*t);else{let t=v*Math.sqrt(y*y-1);i=e=>{let i=Math.exp(-y*v*e),r=Math.min(t*e,300);return a-i*((f+y*v*g)*Math.sinh(r)+t*g*Math.cosh(r))/t}}let b={calculatedDuration:m&&c||null,next:t=>{let e=i(t);if(m)l.done=t>=c;else{let r=0===t?f:0;y<1&&(r=0===t?B(f):t$(i,t,e));let o=Math.abs(a-e)<=s;l.done=Math.abs(r)<=n&&o}return l.value=l.done?a:e,l},toString:()=>{let t=Math.min(tN(b),2e4),e=tU(e=>b.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return b}function tX({keyframes:t,velocity:e=0,power:i=.8,timeConstant:r=325,bounceDamping:n=10,bounceStiffness:s=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:h}){let d,c,p=t[0],m={done:!1,value:p},f=t=>void 0!==a&&t<a||void 0!==l&&t>l,y=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l,g=i*e,v=p+g,x=void 0===o?v:o(v);x!==v&&(g=x-p);let b=t=>-g*Math.exp(-t/r),w=t=>x+b(t),k=t=>{let e=b(t),i=w(t);m.done=Math.abs(e)<=u,m.value=m.done?x:i},A=t=>{f(m.value)&&(d=t,c=tK({keyframes:[m.value,y(m.value)],velocity:t$(w,t,m.value),damping:n,stiffness:s,restDelta:u,restSpeed:h}))};return A(0),{calculatedDuration:null,next:t=>{let e=!1;return(c||void 0!==d||(e=!0,k(t),A(t)),void 0!==d&&t>=d)?c.next(t-d):(e||k(t),m)}}}tK.applyToOptions=t=>{let e=function(t,e=100,i){let r=i({...t,keyframes:[0,e]}),n=Math.min(tN(r),2e4);return{type:"keyframes",ease:t=>r.next(n*t).value/e,duration:I(n)}}(t,100,tK);return t.ease=e.ease,t.duration=B(e.duration),t.type="keyframes",t};let tG=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function tZ(t,e,i,r){if(t===e&&i===r)return u;let n=e=>(function(t,e,i,r,n){let s,o,a=0;do(s=tG(o=e+(i-e)/2,r,n)-t)>0?i=o:e=o;while(Math.abs(s)>1e-7&&++a<12);return o})(e,0,1,t,i);return t=>0===t||1===t?t:tG(n(t),e,r)}let tQ=tZ(.42,0,1,1),tJ=tZ(0,0,.58,1),t0=tZ(.42,0,.58,1),t1=t=>Array.isArray(t)&&"number"!=typeof t[0],t2=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,t5=t=>e=>1-t(1-e),t3=tZ(.33,1.53,.69,.99),t9=t5(t3),t6=t2(t9),t4=t=>(t*=2)<1?.5*t9(t):.5*(2-Math.pow(2,-10*(t-1))),t8=t=>1-Math.sin(Math.acos(t)),t7=t5(t8),et=t2(t8),ee=t=>Array.isArray(t)&&"number"==typeof t[0],ei={linear:u,easeIn:tQ,easeInOut:t0,easeOut:tJ,circIn:t8,circInOut:et,circOut:t7,backIn:t9,backInOut:t6,backOut:t3,anticipate:t4},er=t=>"string"==typeof t,en=t=>{if(ee(t)){$(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,r,n]=t;return tZ(e,i,r,n)}return er(t)?($(void 0!==ei[t],`Invalid easing type '${t}'`),ei[t]):t},es=(t,e,i)=>{let r=e-t;return 0===r?1:(i-t)/r};function eo({duration:t=300,keyframes:e,times:i,ease:r="easeInOut"}){var n;let s=t1(r)?r.map(en):en(r),o={done:!1,value:e[0]},a=function(t,e,{clamp:i=!0,ease:r,mixer:n}={}){let s=t.length;if($(s===e.length,"Both input and output ranges must be the same length"),1===s)return()=>e[0];if(2===s&&e[0]===e[1])return()=>e[1];let o=t[0]===t[1];t[0]>t[s-1]&&(t=[...t].reverse(),e=[...e].reverse());let a=function(t,e,i){let r=[],n=i||h.mix||tB,s=t.length-1;for(let i=0;i<s;i++){let s=n(t[i],t[i+1]);e&&(s=O(Array.isArray(e)?e[i]||u:e,s)),r.push(s)}return r}(e,r,n),l=a.length,d=i=>{if(o&&i<t[0])return e[0];let r=0;if(l>1)for(;r<t.length-2&&!(i<t[r+1]);r++);let n=es(t[r],t[r+1],i);return a[r](n)};return i?e=>d(z(t[0],t[s-1],e)):d}((n=i&&i.length===e.length?i:function(t){let e=[0];return!function(t,e){let i=t[t.length-1];for(let r=1;r<=e;r++){let n=es(0,e,r);t.push(tM(i,1,n))}}(e,t.length-1),e}(e),n.map(e=>e*t)),e,{ease:Array.isArray(s)?s:e.map(()=>s||t0).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(o.value=a(e),o.done=e>=t,o)}}let ea=t=>null!==t;function el(t,{repeat:e,repeatType:i="loop"},r,n=1){let s=t.filter(ea),o=n<0||e&&"loop"!==i&&e%2==1?0:s.length-1;return o&&void 0!==r?r:s[o]}let eu={decay:tX,inertia:tX,tween:eo,keyframes:eo,spring:tK};function eh(t){"string"==typeof t.type&&(t.type=eu[t.type])}class ed{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let ec=t=>t/100;class ep extends ed{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==P.now()&&this.tick(P.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},U.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;eh(t);let{type:e=eo,repeat:i=0,repeatDelay:r=0,repeatType:n,velocity:s=0}=t,{keyframes:o}=t,a=e||eo;a!==eo&&"number"!=typeof o[0]&&(this.mixKeyframes=O(ec,tB(o[0],o[1])),o=[0,100]);let l=a({...t,keyframes:o});"mirror"===n&&(this.mirroredGenerator=a({...t,keyframes:[...o].reverse(),velocity:-s})),null===l.calculatedDuration&&(l.calculatedDuration=tN(l));let{calculatedDuration:u}=l;this.calculatedDuration=u,this.resolvedDuration=u+r,this.totalDuration=this.resolvedDuration*(i+1)-r,this.generator=l}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:r,mixKeyframes:n,mirroredGenerator:s,resolvedDuration:o,calculatedDuration:a}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:u,repeat:h,repeatType:d,repeatDelay:c,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-r/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let y=this.currentTime-l*(this.playbackSpeed>=0?1:-1),g=this.playbackSpeed>=0?y<0:y>r;this.currentTime=Math.max(y,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=r);let v=this.currentTime,x=i;if(h){let t=Math.min(this.currentTime,r)/o,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,h+1))%2&&("reverse"===d?(i=1-i,c&&(i-=c/o)):"mirror"===d&&(x=s)),v=z(0,1,i)*o}let b=g?{done:!1,value:u[0]}:x.next(v);n&&(b.value=n(b.value));let{done:w}=b;g||null===a||(w=this.playbackSpeed>=0?this.currentTime>=r:this.currentTime<=0);let k=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return k&&p!==tX&&(b.value=el(u,this.options,f,this.speed)),m&&m(b.value),k&&this.finish(),b}then(t,e){return this.finished.then(t,e)}get duration(){return I(this.calculatedDuration)}get time(){return I(this.currentTime)}set time(t){t=B(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(P.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=I(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=tI,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(P.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,U.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}let em=t=>180*t/Math.PI,ef=t=>eg(em(Math.atan2(t[1],t[0]))),ey={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:ef,rotateZ:ef,skewX:t=>em(Math.atan(t[1])),skewY:t=>em(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},eg=t=>((t%=360)<0&&(t+=360),t),ev=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),ex=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),eb={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:ev,scaleY:ex,scale:t=>(ev(t)+ex(t))/2,rotateX:t=>eg(em(Math.atan2(t[6],t[5]))),rotateY:t=>eg(em(Math.atan2(-t[2],t[0]))),rotateZ:ef,rotate:ef,skewX:t=>em(Math.atan(t[4])),skewY:t=>em(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function ew(t){return+!!t.includes("scale")}function ek(t,e){let i,r;if(!t||"none"===t)return ew(e);let n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(n)i=eb,r=n;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=ey,r=e}if(!r)return ew(e);let s=i[e],o=r[1].split(",").map(eT);return"function"==typeof s?s(o):o[s]}let eA=(t,e)=>{let{transform:i="none"}=getComputedStyle(t);return ek(i,e)};function eT(t){return parseFloat(t.trim())}let eP=t=>t===K||t===tu,eM=new Set(["x","y","z"]),eS=v.filter(t=>!eM.has(t)),eE={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>ek(e,"x"),y:(t,{transform:e})=>ek(e,"y")};eE.translateX=eE.x,eE.translateY=eE.y;let eV=new Set,eC=!1,eD=!1,ej=!1;function eR(){if(eD){let t=Array.from(eV).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return eS.forEach(i=>{let r=t.getValue(i);void 0!==r&&(e.push([i,r.get()]),r.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}eD=!1,eC=!1,eV.forEach(t=>t.complete(ej)),eV.clear()}function eL(){eV.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(eD=!0)})}class eF{constructor(t,e,i,r,n,s=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=r,this.element=n,this.isAsync=s}scheduleResolve(){this.state="scheduled",this.isAsync?(eV.add(this),eC||(eC=!0,m.read(eL),m.resolveKeyframes(eR))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:r}=this;if(null===t[0]){let n=r?.get(),s=t[t.length-1];if(void 0!==n)t[0]=n;else if(i&&e){let r=i.readValue(e,s);null!=r&&(t[0]=r)}void 0===t[0]&&(t[0]=s),r&&void 0===n&&r.set(t[0])}for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),eV.delete(this)}cancel(){"scheduled"===this.state&&(eV.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let eO=t=>t.startsWith("--");function ez(t){let e;return()=>(void 0===e&&(e=t()),e)}let eB=ez(()=>void 0!==window.ScrollTimeline),eI={},eU=function(t,e){let i=ez(t);return()=>eI[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),eN=([t,e,i,r])=>`cubic-bezier(${t}, ${e}, ${i}, ${r})`,e$={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:eN([0,.65,.55,1]),circOut:eN([.55,0,1,.45]),backIn:eN([.31,.01,.66,-.59]),backOut:eN([.33,1.53,.69,.99])};function eW(t){return"function"==typeof t&&"applyToOptions"in t}class e_ extends ed{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:r,pseudoElement:n,allowFlatten:s=!1,finalKeyframe:o,onComplete:a}=t;this.isPseudoElement=!!n,this.allowFlatten=s,this.options=t,$("string"!=typeof t.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:t,...e}){return eW(t)&&eU()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:r=0,duration:n=300,repeat:s=0,repeatType:o="loop",ease:a="easeOut",times:l}={},u){let h={[e]:i};l&&(h.offset=l);let d=function t(e,i){if(e)return"function"==typeof e?eU()?tU(e,i):"ease-out":ee(e)?eN(e):Array.isArray(e)?e.map(e=>t(e,i)||e$.easeOut):e$[e]}(a,n);Array.isArray(d)&&(h.easing=d),c.value&&U.waapi++;let p={delay:r,duration:n,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:s+1,direction:"reverse"===o?"alternate":"normal"};u&&(p.pseudoElement=u);let m=t.animate(h,p);return c.value&&m.finished.finally(()=>{U.waapi--}),m}(e,i,r,l,n),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!n){let t=el(r,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){eO(e)?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return I(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return I(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=B(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&eB())?(this.animation.timeline=t,u):e(this)}}let eq={anticipate:t4,backInOut:t6,circInOut:et};class eY extends e_{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in eq&&(t.ease=eq[t.ease])}(t),eh(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:r,element:n,...s}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);let o=new ep({...s,autoplay:!1}),a=B(this.finishedTime??this.time);e.setWithVelocity(o.sample(a-10).value,o.sample(a).value,10),o.stop()}}let eH=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tA.test(t)||"0"===t)&&!t.startsWith("url("));function eK(t){return"object"==typeof t&&null!==t}function eX(t){return eK(t)&&"offsetHeight"in t}let eG=new Set(["opacity","clipPath","filter","transform"]),eZ=ez(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class eQ extends ed{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:r=0,repeatDelay:n=0,repeatType:s="loop",keyframes:o,name:a,motionValue:l,element:u,...h}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=P.now();let d={autoplay:t,delay:e,type:i,repeat:r,repeatDelay:n,repeatType:s,name:a,motionValue:l,element:u,...h},c=u?.KeyframeResolver||eF;this.keyframeResolver=new c(o,(t,e,i)=>this.onKeyframesResolved(t,e,d,!i),a,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,r){this.keyframeResolver=void 0;let{name:n,type:s,velocity:o,delay:a,isHandoff:l,onUpdate:d}=i;this.resolvedAt=P.now(),!function(t,e,i,r){let n=t[0];if(null===n)return!1;if("display"===e||"visibility"===e)return!0;let s=t[t.length-1],o=eH(n,e),a=eH(s,e);return N(o===a,`You are trying to animate ${e} from "${n}" to "${s}". ${n} is not an animatable value - to enable this animation set ${n} to a value animatable to ${s} via the \`style\` property.`),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||eW(i))&&r)}(t,n,s,o)&&((h.instantAnimations||!a)&&d?.(el(t,i,e)),t[0]=t[t.length-1],i.duration=0,i.repeat=0);let c={startTime:r?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},p=!l&&function(t){let{motionValue:e,name:i,repeatDelay:r,repeatType:n,damping:s,type:o}=t;if(!eX(e?.owner?.current))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return eZ()&&i&&eG.has(i)&&("transform"!==i||!l)&&!a&&!r&&"mirror"!==n&&0!==s&&"inertia"!==o}(c)?new eY({...c,element:c.motionValue.owner.current}):new ep(c);p.finished.then(()=>this.notifyFinished()).catch(u),this.pendingTimeline&&(this.stopTimeline=p.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=p}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),ej=!0,eL(),eR(),ej=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let eJ=t=>null!==t,e0={type:"spring",stiffness:500,damping:25,restSpeed:10},e1=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),e2={type:"keyframes",duration:.8},e5={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},e3=(t,{keyframes:e})=>e.length>2?e2:x.has(t)?t.startsWith("scale")?e1(e[1]):e0:e5,e9=(t,e,i,r={},n,s)=>o=>{let a=l(r,t)||{},u=a.delay||r.delay||0,{elapsed:d=0}=r;d-=B(u);let c={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-d,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:s?void 0:n};!function({when:t,delay:e,delayChildren:i,staggerChildren:r,staggerDirection:n,repeat:s,repeatType:o,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(a)&&Object.assign(c,e3(t,c)),c.duration&&(c.duration=B(c.duration)),c.repeatDelay&&(c.repeatDelay=B(c.repeatDelay)),void 0!==c.from&&(c.keyframes[0]=c.from);let p=!1;if(!1!==c.type&&(0!==c.duration||c.repeatDelay)||(c.duration=0,0===c.delay&&(p=!0)),(h.instantAnimations||h.skipAnimations)&&(p=!0,c.duration=0,c.delay=0),c.allowFlatten=!a.type&&!a.ease,p&&!s&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:i="loop"},r){let n=t.filter(eJ),s=e&&"loop"!==i&&e%2==1?0:n.length-1;return n[s]}(c.keyframes,a);if(void 0!==t)return void m.update(()=>{c.onUpdate(t),c.onComplete()})}return a.isSync?new ep(c):new eQ(c)};function e6(t,e,{delay:i=0,transitionOverride:r,type:n}={}){let{transition:s=t.getDefaultTransition(),transitionEnd:o,...u}=e;r&&(s=r);let h=[],d=n&&t.animationState&&t.animationState.getState()[n];for(let e in u){let r=t.getValue(e,t.latestValues[e]??null),n=u[e];if(void 0===n||d&&function({protectedKeys:t,needsAnimating:e},i){let r=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,r}(d,e))continue;let o={delay:i,...l(s||{},e)},a=r.get();if(void 0!==a&&!r.isAnimating&&!Array.isArray(n)&&n===a&&!o.velocity)continue;let c=!1;if(window.MotionHandoffAnimation){let i=t.props[L];if(i){let t=window.MotionHandoffAnimation(i,e,m);null!==t&&(o.startTime=t,c=!0)}}j(t,e),r.start(e9(e,r,n,t.shouldReduceMotion&&b.has(e)?{type:!1}:o,t,c));let p=r.animation;p&&h.push(p)}return o&&Promise.all(h).then(()=>{m.update(()=>{o&&function(t,e){let{transitionEnd:i={},transition:r={},...n}=a(t,e)||{};for(let e in n={...n,...i}){var s;let i=C(s=n[e])?s[s.length-1]||0:s;t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,V(i))}}(t,o)})}),h}function e4(t,e,i={}){let r=a(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:n=t.getDefaultTransition()||{}}=r||{};i.transitionOverride&&(n=i.transitionOverride);let s=r?()=>Promise.all(e6(t,r,i)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(r=0)=>{let{delayChildren:s=0,staggerChildren:o,staggerDirection:a}=n;return function(t,e,i=0,r=0,n=1,s){let o=[],a=(t.variantChildren.size-1)*r,l=1===n?(t=0)=>t*r:(t=0)=>a-t*r;return Array.from(t.variantChildren).sort(e8).forEach((t,r)=>{t.notify("AnimationStart",e),o.push(e4(t,e,{...s,delay:i+l(r)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(o)}(t,e,s+r,o,a,i)}:()=>Promise.resolve(),{when:l}=n;if(!l)return Promise.all([s(),o(i.delay)]);{let[t,e]="beforeChildren"===l?[s,o]:[o,s];return t().then(()=>e())}}function e8(t,e){return t.sortNodePosition(e)}function e7(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let r=0;r<i;r++)if(e[r]!==t[r])return!1;return!0}function it(t){return"string"==typeof t||Array.isArray(t)}let ie=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],ii=["initial",...ie],ir=ii.length,is=[...ie].reverse(),io=ie.length;function ia(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function il(){return{animate:ia(!0),whileInView:ia(),whileHover:ia(),whileTap:ia(),whileDrag:ia(),whileFocus:ia(),exit:ia()}}class iu{constructor(t){this.isMounted=!1,this.node=t}update(){}}class ih extends iu{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let r;if(t.notify("AnimationStart",e),Array.isArray(e))r=Promise.all(e.map(e=>e4(t,e,i)));else if("string"==typeof e)r=e4(t,e,i);else{let n="function"==typeof e?a(t,e,i.custom):e;r=Promise.all(e6(t,n,i))}return r.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=il(),r=!0,s=e=>(i,r)=>{let n=a(t,r,"exit"===e?t.presenceContext?.custom:void 0);if(n){let{transition:t,transitionEnd:e,...r}=n;i={...i,...r,...e}}return i};function o(o){let{props:l}=t,u=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<ir;t++){let r=ii[t],n=e.props[r];(it(n)||!1===n)&&(i[r]=n)}return i}(t.parent)||{},h=[],d=new Set,c={},p=1/0;for(let e=0;e<io;e++){var m,f;let a=is[e],y=i[a],g=void 0!==l[a]?l[a]:u[a],v=it(g),x=a===o?y.isActive:null;!1===x&&(p=e);let b=g===u[a]&&g!==l[a]&&v;if(b&&r&&t.manuallyAnimateOnMount&&(b=!1),y.protectedKeys={...c},!y.isActive&&null===x||!g&&!y.prevProp||n(g)||"boolean"==typeof g)continue;let w=(m=y.prevProp,"string"==typeof(f=g)?f!==m:!!Array.isArray(f)&&!e7(f,m)),k=w||a===o&&y.isActive&&!b&&v||e>p&&v,A=!1,T=Array.isArray(g)?g:[g],P=T.reduce(s(a),{});!1===x&&(P={});let{prevResolvedValues:M={}}=y,S={...M,...P},E=e=>{k=!0,d.has(e)&&(A=!0,d.delete(e)),y.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in S){let e=P[t],i=M[t];if(c.hasOwnProperty(t))continue;let r=!1;(C(e)&&C(i)?e7(e,i):e===i)?void 0!==e&&d.has(t)?E(t):y.protectedKeys[t]=!0:null!=e?E(t):d.add(t)}y.prevProp=g,y.prevResolvedValues=P,y.isActive&&(c={...c,...P}),r&&t.blockInitialAnimation&&(k=!1);let V=!(b&&w)||A;k&&V&&h.push(...T.map(t=>({animation:t,options:{type:a}})))}if(d.size){let e={};if("boolean"!=typeof l.initial){let i=a(t,Array.isArray(l.initial)?l.initial[0]:l.initial);i&&i.transition&&(e.transition=i.transition)}d.forEach(i=>{let r=t.getBaseTarget(i),n=t.getValue(i);n&&(n.liveStyle=!0),e[i]=r??null}),h.push({animation:e})}let y=!!h.length;return r&&(!1===l.initial||l.initial===l.animate)&&!t.manuallyAnimateOnMount&&(y=!1),r=!1,y?e(h):Promise.resolve()}return{animateChanges:o,setActive:function(e,r){if(i[e].isActive===r)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,r)),i[e].isActive=r;let n=o(e);for(let t in i)i[t].protectedKeys={};return n},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=il(),r=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();n(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let id=0;class ic extends iu{constructor(){super(...arguments),this.id=id++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let r=this.node.animationState.setActive("exit",!t);e&&!t&&r.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}let ip={x:!1,y:!1};function im(t,e,i,r={passive:!0}){return t.addEventListener(e,i,r),()=>t.removeEventListener(e,i)}let iy=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function ig(t){return{point:{x:t.pageX,y:t.pageY}}}let iv=t=>e=>iy(e)&&t(e,ig(e));function ix(t,e,i,r){return im(t,e,iv(i),r)}function ib({top:t,left:e,right:i,bottom:r}){return{x:{min:e,max:i},y:{min:t,max:r}}}function iw(t){return t.max-t.min}function ik(t,e,i,r=.5){t.origin=r,t.originPoint=tM(e.min,e.max,t.origin),t.scale=iw(i)/iw(e),t.translate=tM(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function iA(t,e,i,r){ik(t.x,e.x,i.x,r?r.originX:void 0),ik(t.y,e.y,i.y,r?r.originY:void 0)}function iT(t,e,i){t.min=i.min+e.min,t.max=t.min+iw(e)}function iP(t,e,i){t.min=e.min-i.min,t.max=t.min+iw(e)}function iM(t,e,i){iP(t.x,e.x,i.x),iP(t.y,e.y,i.y)}let iS=()=>({translate:0,scale:1,origin:0,originPoint:0}),iE=()=>({x:iS(),y:iS()}),iV=()=>({min:0,max:0}),iC=()=>({x:iV(),y:iV()});function iD(t){return[t("x"),t("y")]}function ij(t){return void 0===t||1===t}function iR({scale:t,scaleX:e,scaleY:i}){return!ij(t)||!ij(e)||!ij(i)}function iL(t){return iR(t)||iF(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function iF(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function iO(t,e,i,r,n){return void 0!==n&&(t=r+n*(t-r)),r+i*(t-r)+e}function iz(t,e=0,i=1,r,n){t.min=iO(t.min,e,i,r,n),t.max=iO(t.max,e,i,r,n)}function iB(t,{x:e,y:i}){iz(t.x,e.translate,e.scale,e.originPoint),iz(t.y,i.translate,i.scale,i.originPoint)}function iI(t,e){t.min=t.min+e,t.max=t.max+e}function iU(t,e,i,r,n=.5){let s=tM(t.min,t.max,n);iz(t,e,i,s,r)}function iN(t,e){iU(t.x,e.x,e.scaleX,e.scale,e.originX),iU(t.y,e.y,e.scaleY,e.scale,e.originY)}function i$(t,e){return ib(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),r=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:r.y,right:r.x}}(t.getBoundingClientRect(),e))}let iW=({current:t})=>t?t.ownerDocument.defaultView:null;function i_(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let iq=(t,e)=>Math.abs(t-e);class iY{constructor(t,e,{transformPagePoint:i,contextWindow:r,dragSnapToOrigin:n=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=iX(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(iq(t.x,e.x)**2+iq(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;let{point:r}=t,{timestamp:n}=y;this.history.push({...r,timestamp:n});let{onStart:s,onMove:o}=this.handlers;e||(s&&s(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=iH(e,this.transformPagePoint),m.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:r,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=iX("pointercancel"===t.type?this.lastMoveEventInfo:iH(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,s),r&&r(t,s)},!iy(t))return;this.dragSnapToOrigin=n,this.handlers=e,this.transformPagePoint=i,this.contextWindow=r||window;let s=iH(ig(t),this.transformPagePoint),{point:o}=s,{timestamp:a}=y;this.history=[{...o,timestamp:a}];let{onSessionStart:l}=e;l&&l(t,iX(s,this.history)),this.removeListeners=O(ix(this.contextWindow,"pointermove",this.handlePointerMove),ix(this.contextWindow,"pointerup",this.handlePointerUp),ix(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),f(this.updatePoint)}}function iH(t,e){return e?{point:e(t.point)}:t}function iK(t,e){return{x:t.x-e.x,y:t.y-e.y}}function iX({point:t},e){return{point:t,delta:iK(t,iG(e)),offset:iK(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,r=null,n=iG(t);for(;i>=0&&(r=t[i],!(n.timestamp-r.timestamp>B(.1)));)i--;if(!r)return{x:0,y:0};let s=I(n.timestamp-r.timestamp);if(0===s)return{x:0,y:0};let o={x:(n.x-r.x)/s,y:(n.y-r.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,.1)}}function iG(t){return t[t.length-1]}function iZ(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function iQ(t,e){let i=e.min-t.min,r=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,r]=[r,i]),{min:i,max:r}}function iJ(t,e,i){return{min:i0(t,e),max:i0(t,i)}}function i0(t,e){return"number"==typeof t?t:t[e]||0}let i1=new WeakMap;class i2{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=iC(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:r}=this.getProps();this.panSession=new iY(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(ig(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:r,onDragStart:n}=this.getProps();if(i&&!r&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(ip[t])return null;else return ip[t]=!0,()=>{ip[t]=!1};return ip.x||ip.y?null:(ip.x=ip.y=!0,()=>{ip.x=ip.y=!1})}(i),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),iD(t=>{let e=this.getAxisMotionValue(t).get()||0;if(tl.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let r=i.layout.layoutBox[t];r&&(e=iw(r)*(parseFloat(e)/100))}}this.originPoint[t]=e}),n&&m.postRender(()=>n(t,e)),j(this.visualElement,"transform");let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:r,onDirectionLock:n,onDrag:s}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:o}=e;if(r&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),s&&s(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>iD(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:r,contextWindow:iW(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:r}=e;this.startAnimation(r);let{onDragEnd:n}=this.getProps();n&&m.postRender(()=>n(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:r}=this.getProps();if(!i||!i5(t,r,this.currentDirection))return;let n=this.getAxisMotionValue(t),s=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(s=function(t,{min:e,max:i},r){return void 0!==e&&t<e?t=r?tM(e,t,r.min):Math.max(t,e):void 0!==i&&t>i&&(t=r?tM(i,t,r.max):Math.min(t,i)),t}(s,this.constraints[t],this.elastic[t])),n.set(s)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,r=this.constraints;t&&i_(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:r,right:n}){return{x:iZ(t.x,i,n),y:iZ(t.y,e,r)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:iJ(t,"left","right"),y:iJ(t,"top","bottom")}}(e),r!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&iD(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!i_(e))return!1;let r=e.current;$(null!==r,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let s=function(t,e,i){let r=i$(t,i),{scroll:n}=e;return n&&(iI(r.x,n.offset.x),iI(r.y,n.offset.y)),r}(r,n.root,this.visualElement.getTransformPagePoint()),o=(t=n.layout.layoutBox,{x:iQ(t.x,s.x),y:iQ(t.y,s.y)});if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=ib(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:r,dragTransition:n,dragSnapToOrigin:s,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(iD(o=>{if(!i5(o,e,this.currentDirection))return;let l=a&&a[o]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[o]:0,bounceStiffness:r?200:1e6,bounceDamping:r?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return j(this.visualElement,t),i.start(e9(t,i,0,e,this.visualElement,!1))}stopAnimation(){iD(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){iD(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){iD(e=>{let{drag:i}=this.getProps();if(!i5(e,i,this.currentDirection))return;let{projection:r}=this.visualElement,n=this.getAxisMotionValue(e);if(r&&r.layout){let{min:i,max:s}=r.layout.layoutBox[e];n.set(t[e]-tM(i,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!i_(e)||!i||!this.constraints)return;this.stopAnimation();let r={x:0,y:0};iD(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();r[t]=function(t,e){let i=.5,r=iw(t),n=iw(e);return n>r?i=es(e.min,e.max-r,t.min):r>n&&(i=es(t.min,t.max-n,e.min)),z(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),iD(e=>{if(!i5(e,t,null))return;let i=this.getAxisMotionValue(e),{min:n,max:s}=this.constraints[e];i.set(tM(n,s,r[e]))})}addListeners(){if(!this.visualElement.current)return;i1.set(this.visualElement,this);let t=ix(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();i_(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,r=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),m.read(e);let n=im(window,"resize",()=>this.scalePositionWithinConstraints()),s=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(iD(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{n(),t(),r(),s&&s()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:r=!1,dragConstraints:n=!1,dragElastic:s=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:r,dragConstraints:n,dragElastic:s,dragMomentum:o}}}function i5(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class i3 extends iu{constructor(t){super(t),this.removeGroupControls=u,this.removeListeners=u,this.controls=new i2(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||u}unmount(){this.removeGroupControls(),this.removeListeners()}}let i9=t=>(e,i)=>{t&&m.postRender(()=>t(e,i))};class i6 extends iu{constructor(){super(...arguments),this.removePointerDownListener=u}onPointerDown(t){this.session=new iY(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:iW(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:r}=this.node.getProps();return{onSessionStart:i9(t),onStart:i9(e),onMove:i,onEnd:(t,e)=>{delete this.session,r&&m.postRender(()=>r(t,e))}}}mount(){this.removePointerDownListener=ix(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var i4,i8,i7=i(5155);let{schedule:rt}=p(queueMicrotask,!1);var re=i(2115);let ri=(0,re.createContext)(null),rr=(0,re.createContext)({}),rn=(0,re.createContext)({}),rs={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function ro(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let ra={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!tu.test(t))return t;else t=parseFloat(t);let i=ro(t,e.target.x),r=ro(t,e.target.y);return`${i}% ${r}%`}},rl={};class ru extends re.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:r}=this.props,{projection:n}=t;for(let t in rd)rl[t]=rd[t],_(t)&&(rl[t].isCSSVariable=!0);n&&(e.group&&e.group.add(n),i&&i.register&&r&&i.register(n),n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),rs.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:r,isPresent:n}=this.props,{projection:s}=i;return s&&(s.isPresent=n,r||t.layoutDependency!==e||void 0===e||t.isPresent!==n?s.willUpdate():this.safeToRemove(),t.isPresent!==n&&(n?s.promote():s.relegate()||m.postRender(()=>{let t=s.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),rt.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:r}=t;r&&(r.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(r),i&&i.deregister&&i.deregister(r))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function rh(t){let[e,i]=function(t=!0){let e=(0,re.useContext)(ri);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:r,register:n}=e,s=(0,re.useId)();(0,re.useEffect)(()=>{if(t)return n(s)},[t]);let o=(0,re.useCallback)(()=>t&&r&&r(s),[s,r,t]);return!i&&r?[!1,o]:[!0]}(),r=(0,re.useContext)(rr);return(0,i7.jsx)(ru,{...t,layoutGroup:r,switchLayoutGroup:(0,re.useContext)(rn),isPresent:e,safeToRemove:i})}let rd={borderRadius:{...ra,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ra,borderTopRightRadius:ra,borderBottomLeftRadius:ra,borderBottomRightRadius:ra,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let r=tA.parse(t);if(r.length>5)return t;let n=tA.createTransformer(t),s=+("number"!=typeof r[0]),o=i.x.scale*e.x,a=i.y.scale*e.y;r[0+s]/=o,r[1+s]/=a;let l=tM(o,a,.5);return"number"==typeof r[2+s]&&(r[2+s]/=l),"number"==typeof r[3+s]&&(r[3+s]/=l),n(r)}}};function rc(t){return eK(t)&&"ownerSVGElement"in t}let rp=(t,e)=>t.depth-e.depth;class rm{constructor(){this.children=[],this.isDirty=!1}add(t){w(this.children,t),this.isDirty=!0}remove(t){k(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(rp),this.isDirty=!1,this.children.forEach(t)}}function rf(t){return D(t)?t.get():t}let ry=["TopLeft","TopRight","BottomLeft","BottomRight"],rg=ry.length,rv=t=>"string"==typeof t?parseFloat(t):t,rx=t=>"number"==typeof t||tu.test(t);function rb(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let rw=rA(0,.5,t7),rk=rA(.5,.95,u);function rA(t,e,i){return r=>r<t?0:r>e?1:i(es(t,e,r))}function rT(t,e){t.min=e.min,t.max=e.max}function rP(t,e){rT(t.x,e.x),rT(t.y,e.y)}function rM(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function rS(t,e,i,r,n){return t-=e,t=r+1/i*(t-r),void 0!==n&&(t=r+1/n*(t-r)),t}function rE(t,e,[i,r,n],s,o){!function(t,e=0,i=1,r=.5,n,s=t,o=t){if(tl.test(e)&&(e=parseFloat(e),e=tM(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=tM(s.min,s.max,r);t===s&&(a-=e),t.min=rS(t.min,e,i,a,n),t.max=rS(t.max,e,i,a,n)}(t,e[i],e[r],e[n],e.scale,s,o)}let rV=["x","scaleX","originX"],rC=["y","scaleY","originY"];function rD(t,e,i,r){rE(t.x,e,rV,i?i.x:void 0,r?r.x:void 0),rE(t.y,e,rC,i?i.y:void 0,r?r.y:void 0)}function rj(t){return 0===t.translate&&1===t.scale}function rR(t){return rj(t.x)&&rj(t.y)}function rL(t,e){return t.min===e.min&&t.max===e.max}function rF(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function rO(t,e){return rF(t.x,e.x)&&rF(t.y,e.y)}function rz(t){return iw(t.x)/iw(t.y)}function rB(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class rI{constructor(){this.members=[]}add(t){w(this.members,t),t.scheduleRender()}remove(t){if(k(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:r}=t.options;!1===r&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let rU={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},rN=["","X","Y","Z"],r$={visibility:"hidden"},rW=0;function r_(t,e,i,r){let{latestValues:n}=e;n[t]&&(i[t]=n[t],e.setStaticValue(t,0),r&&(r[t]=0))}function rq({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:r,resetTransform:n}){return class{constructor(t={},i=e?.()){this.id=rW++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,c.value&&(rU.nodes=rU.calculatedTargetDeltas=rU.calculatedProjections=0),this.nodes.forEach(rK),this.nodes.forEach(r1),this.nodes.forEach(r2),this.nodes.forEach(rX),c.addProjectionMetrics&&c.addProjectionMetrics(rU)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new rm)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new A),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=rc(e)&&!(rc(e)&&"svg"===e.tagName),this.instance=e;let{layoutId:i,layout:r,visualElement:n}=this.options;if(n&&!n.current&&n.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(r||i)&&(this.isLayoutDirty=!0),t){let i,r=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=P.now(),r=({timestamp:n})=>{let s=n-i;s>=250&&(f(r),t(s-e))};return m.setup(r,!0),()=>f(r)}(r,250),rs.hasAnimatedSinceResize&&(rs.hasAnimatedSinceResize=!1,this.nodes.forEach(r0))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&n&&(i||r)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:r})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let s=this.options.transition||n.getDefaultTransition()||r8,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=n.getProps(),u=!this.targetLayout||!rO(this.targetLayout,r),h=!e&&i;if(this.options.layoutRoot||this.resumeFrom||h||e&&(u||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...l(s,"layout"),onPlay:o,onComplete:a};(n.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,h)}else e||r0(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=r})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),f(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(r5),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let r=i.props[L];if(window.MotionHasOptimisedAnimation(r,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(r,"transform",m,!(t||i))}let{parent:n}=e;n&&!n.hasCheckedOptimisedAppear&&t(n)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(rZ);return}this.isUpdating||this.nodes.forEach(rQ),this.isUpdating=!1,this.nodes.forEach(rJ),this.nodes.forEach(rY),this.nodes.forEach(rH),this.clearAllSnapshots();let t=P.now();y.delta=z(0,1e3/60,t-y.timestamp),y.timestamp=t,y.isProcessing=!0,g.update.process(y),g.preRender.process(y),g.render.process(y),y.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,rt.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(rG),this.sharedNodes.forEach(r3)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,m.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){m.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||iw(this.snapshot.measuredBox.x)||iw(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=iC(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=r(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!n)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!rR(this.projectionDelta),i=this.getTransformTemplate(),r=i?i(this.latestValues,""):void 0,s=r!==this.prevTransformTemplateValue;t&&this.instance&&(e||iL(this.latestValues)||s)&&(n(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),r=this.removeElementScroll(i);return t&&(r=this.removeTransform(r)),ne((e=r).x),ne(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return iC();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(nr))){let{scroll:t}=this.root;t&&(iI(e.x,t.offset.x),iI(e.y,t.offset.y))}return e}removeElementScroll(t){let e=iC();if(rP(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let r=this.path[i],{scroll:n,options:s}=r;r!==this.root&&n&&s.layoutScroll&&(n.wasRoot&&rP(e,t),iI(e.x,n.offset.x),iI(e.y,n.offset.y))}return e}applyTransform(t,e=!1){let i=iC();rP(i,t);for(let t=0;t<this.path.length;t++){let r=this.path[t];!e&&r.options.layoutScroll&&r.scroll&&r!==r.root&&iN(i,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),iL(r.latestValues)&&iN(i,r.latestValues)}return iL(this.latestValues)&&iN(i,this.latestValues),i}removeTransform(t){let e=iC();rP(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!iL(i.latestValues))continue;iR(i.latestValues)&&i.updateSnapshot();let r=iC();rP(r,i.measurePageBox()),rD(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,r)}return iL(this.latestValues)&&rD(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==y.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:r,layoutId:n}=this.options;if(this.layout&&(r||n)){if(this.resolvedRelativeTargetAt=y.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iC(),this.relativeTargetOrigin=iC(),iM(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),rP(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=iC(),this.targetWithTransforms=iC()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var s,o,a;this.forceRelativeParentToResolveTarget(),s=this.target,o=this.relativeTarget,a=this.relativeParent.target,iT(s.x,o.x,a.x),iT(s.y,o.y,a.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):rP(this.target,this.layout.layoutBox),iB(this.target,this.targetDelta)):rP(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iC(),this.relativeTargetOrigin=iC(),iM(this.relativeTargetOrigin,this.target,t.target),rP(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}c.value&&rU.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||iR(this.parent.latestValues)||iF(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===y.timestamp&&(i=!1),i)return;let{layout:r,layoutId:n}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(r||n))return;rP(this.layoutCorrected,this.layout.layoutBox);let s=this.treeScale.x,o=this.treeScale.y;!function(t,e,i,r=!1){let n,s,o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){s=(n=i[a]).projectionDelta;let{visualElement:o}=n.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(r&&n.options.layoutScroll&&n.scroll&&n!==n.root&&iN(t,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),s&&(e.x*=s.x.scale,e.y*=s.y.scale,iB(t,s)),r&&iL(n.latestValues)&&iN(t,n.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=iC());let{target:a}=t;if(!a){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(rM(this.prevProjectionDelta.x,this.projectionDelta.x),rM(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),iA(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===s&&this.treeScale.y===o&&rB(this.projectionDelta.x,this.prevProjectionDelta.x)&&rB(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),c.value&&rU.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=iE(),this.projectionDelta=iE(),this.projectionDeltaWithTransform=iE()}setAnimationOrigin(t,e=!1){let i,r=this.snapshot,n=r?r.latestValues:{},s={...this.latestValues},o=iE();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=iC(),l=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,d=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(r4));this.animationProgress=0,this.mixTargetDelta=e=>{let r=e/1e3;if(r9(o.x,t.x,r),r9(o.y,t.y,r),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,c,p,m,f,y;iM(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=a,y=r,r6(p.x,m.x,f.x,y),r6(p.y,m.y,f.y,y),i&&(u=this.relativeTarget,c=i,rL(u.x,c.x)&&rL(u.y,c.y))&&(this.isProjectionDirty=!1),i||(i=iC()),rP(i,this.relativeTarget)}l&&(this.animationValues=s,function(t,e,i,r,n,s){n?(t.opacity=tM(0,i.opacity??1,rw(r)),t.opacityExit=tM(e.opacity??1,0,rk(r))):s&&(t.opacity=tM(e.opacity??1,i.opacity??1,r));for(let n=0;n<rg;n++){let s=`border${ry[n]}Radius`,o=rb(e,s),a=rb(i,s);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||rx(o)===rx(a)?(t[s]=Math.max(tM(rv(o),rv(a),r),0),(tl.test(a)||tl.test(o))&&(t[s]+="%")):t[s]=a)}(e.rotate||i.rotate)&&(t.rotate=tM(e.rotate||0,i.rotate||0,r))}(s,n,this.latestValues,r,d,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(f(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=m.update(()=>{rs.hasAnimatedSinceResize=!0,U.layout++,this.motionValue||(this.motionValue=V(0)),this.currentAnimation=function(t,e,i){let r=D(t)?t:V(t);return r.start(e9("",r,e,i)),r.animation}(this.motionValue,[0,1e3],{...t,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{U.layout--},onComplete:()=>{U.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:r,latestValues:n}=t;if(e&&i&&r){if(this!==t&&this.layout&&r&&ni(this.options.animationType,this.layout.layoutBox,r.layoutBox)){i=this.target||iC();let e=iw(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let r=iw(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+r}rP(e,i),iN(e,n),iA(this.projectionDeltaWithTransform,this.layoutCorrected,e,n)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new rI),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let r=this.getStack();r&&r.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let r={};i.z&&r_("z",t,r,this.animationValues);for(let e=0;e<rN.length;e++)r_(`rotate${rN[e]}`,t,r,this.animationValues),r_(`skew${rN[e]}`,t,r,this.animationValues);for(let e in t.render(),r)t.setStaticValue(e,r[e]),this.animationValues&&(this.animationValues[e]=r[e]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return r$;let e={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=rf(t?.pointerEvents)||"",e.transform=i?i(this.latestValues,""):"none",e;let r=this.getLead();if(!this.projectionDelta||!this.layout||!r.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=rf(t?.pointerEvents)||""),this.hasProjected&&!iL(this.latestValues)&&(e.transform=i?i({},""):"none",this.hasProjected=!1),e}let n=r.animationValues||r.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,i){let r="",n=t.x.translate/e.x,s=t.y.translate/e.y,o=i?.z||0;if((n||s||o)&&(r=`translate3d(${n}px, ${s}px, ${o}px) `),(1!==e.x||1!==e.y)&&(r+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:n,rotateY:s,skewX:o,skewY:a}=i;t&&(r=`perspective(${t}px) ${r}`),e&&(r+=`rotate(${e}deg) `),n&&(r+=`rotateX(${n}deg) `),s&&(r+=`rotateY(${s}deg) `),o&&(r+=`skewX(${o}deg) `),a&&(r+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(r+=`scale(${a}, ${l})`),r||"none"}(this.projectionDeltaWithTransform,this.treeScale,n),i&&(e.transform=i(n,e.transform));let{x:s,y:o}=this.projectionDelta;for(let t in e.transformOrigin=`${100*s.origin}% ${100*o.origin}% 0`,r.animationValues?e.opacity=r===this?n.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:n.opacityExit:e.opacity=r===this?void 0!==n.opacity?n.opacity:"":void 0!==n.opacityExit?n.opacityExit:0,rl){if(void 0===n[t])continue;let{correct:i,applyTo:s,isCSSVariable:o}=rl[t],a="none"===e.transform?n[t]:i(n[t],r);if(s){let t=s.length;for(let i=0;i<t;i++)e[s[i]]=a}else o?this.options.visualElement.renderState.vars[t]=a:e[t]=a}return this.options.layoutId&&(e.pointerEvents=r===this?rf(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(rZ),this.root.sharedNodes.clear()}}}function rY(t){t.updateLayout()}function rH(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:r}=t.layout,{animationType:n}=t.options,s=e.source!==t.layout.source;"size"===n?iD(t=>{let r=s?e.measuredBox[t]:e.layoutBox[t],n=iw(r);r.min=i[t].min,r.max=r.min+n}):ni(n,e.layoutBox,i)&&iD(r=>{let n=s?e.measuredBox[r]:e.layoutBox[r],o=iw(i[r]);n.max=n.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[r].max=t.relativeTarget[r].min+o)});let o=iE();iA(o,i,e.layoutBox);let a=iE();s?iA(a,t.applyTransform(r,!0),e.measuredBox):iA(a,i,e.layoutBox);let l=!rR(o),u=!1;if(!t.resumeFrom){let r=t.getClosestProjectingParent();if(r&&!r.resumeFrom){let{snapshot:n,layout:s}=r;if(n&&s){let o=iC();iM(o,e.layoutBox,n.layoutBox);let a=iC();iM(a,i,s.layoutBox),rO(o,a)||(u=!0),r.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=r)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function rK(t){c.value&&rU.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function rX(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function rG(t){t.clearSnapshot()}function rZ(t){t.clearMeasurements()}function rQ(t){t.isLayoutDirty=!1}function rJ(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function r0(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function r1(t){t.resolveTargetDelta()}function r2(t){t.calcProjection()}function r5(t){t.resetSkewAndRotation()}function r3(t){t.removeLeadSnapshot()}function r9(t,e,i){t.translate=tM(e.translate,0,i),t.scale=tM(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function r6(t,e,i,r){t.min=tM(e.min,i.min,r),t.max=tM(e.max,i.max,r)}function r4(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let r8={duration:.45,ease:[.4,0,.1,1]},r7=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),nt=r7("applewebkit/")&&!r7("chrome/")?Math.round:u;function ne(t){t.min=nt(t.min),t.max=nt(t.max)}function ni(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(rz(e)-rz(i)))}function nr(t){return t!==t.root&&t.scroll?.wasRoot}let nn=rq({attachResizeListener:(t,e)=>im(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ns={current:void 0},no=rq({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!ns.current){let t=new nn({});t.mount(window),t.setOptions({layoutScroll:!0}),ns.current=t}return ns.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function na(t,e){let i=function(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,i=(void 0)??e.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),r=new AbortController;return[i,{passive:!0,...e,signal:r.signal},()=>r.abort()]}function nl(t){return!("touch"===t.pointerType||ip.x||ip.y)}function nu(t,e,i){let{props:r}=t;t.animationState&&r.whileHover&&t.animationState.setActive("whileHover","Start"===i);let n=r["onHover"+i];n&&m.postRender(()=>n(e,ig(e)))}class nh extends iu{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[r,n,s]=na(t,i),o=t=>{if(!nl(t))return;let{target:i}=t,r=e(i,t);if("function"!=typeof r||!i)return;let s=t=>{nl(t)&&(r(t),i.removeEventListener("pointerleave",s))};i.addEventListener("pointerleave",s,n)};return r.forEach(t=>{t.addEventListener("pointerenter",o,n)}),s}(t,(t,e)=>(nu(this.node,e,"Start"),t=>nu(this.node,t,"End"))))}unmount(){}}class nd extends iu{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=O(im(this.node.current,"focus",()=>this.onFocus()),im(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let nc=(t,e)=>!!e&&(t===e||nc(t,e.parentElement)),np=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),nm=new WeakSet;function nf(t){return e=>{"Enter"===e.key&&t(e)}}function ny(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let ng=(t,e)=>{let i=t.currentTarget;if(!i)return;let r=nf(()=>{if(nm.has(i))return;ny(i,"down");let t=nf(()=>{ny(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>ny(i,"cancel"),e)});i.addEventListener("keydown",r,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",r),e)};function nv(t){return iy(t)&&!(ip.x||ip.y)}function nx(t,e,i){let{props:r}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&r.whileTap&&t.animationState.setActive("whileTap","Start"===i);let n=r["onTap"+("End"===i?"":i)];n&&m.postRender(()=>n(e,ig(e)))}class nb extends iu{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[r,n,s]=na(t,i),o=t=>{let r=t.currentTarget;if(!nv(t))return;nm.add(r);let s=e(r,t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),nm.has(r)&&nm.delete(r),nv(t)&&"function"==typeof s&&s(t,{success:e})},a=t=>{o(t,r===window||r===document||i.useGlobalTarget||nc(r,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,n),window.addEventListener("pointercancel",l,n)};return r.forEach(t=>{((i.useGlobalTarget?window:t).addEventListener("pointerdown",o,n),eX(t))&&(t.addEventListener("focus",t=>ng(t,n)),np.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),s}(t,(t,e)=>(nx(this.node,e,"Start"),(t,{success:e})=>nx(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let nw=new WeakMap,nk=new WeakMap,nA=t=>{let e=nw.get(t.target);e&&e(t)},nT=t=>{t.forEach(nA)},nP={some:0,all:1};class nM extends iu{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:r="some",once:n}=t,s={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof r?r:nP[r]};return function(t,e,i){let r=function({root:t,...e}){let i=t||document;nk.has(i)||nk.set(i,{});let r=nk.get(i),n=JSON.stringify(e);return r[n]||(r[n]=new IntersectionObserver(nT,{root:t,...e})),r[n]}(e);return nw.set(t,i),r.observe(t),()=>{nw.delete(t),r.unobserve(t)}}(this.node.current,s,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,n&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:r}=this.node.getProps(),s=e?i:r;s&&s(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let nS=(0,re.createContext)({strict:!1}),nE=(0,re.createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),nV=(0,re.createContext)({});function nC(t){return n(t.animate)||ii.some(e=>it(t[e]))}function nD(t){return!!(nC(t)||t.variants)}function nj(t){return Array.isArray(t)?t.join(" "):t}let nR="undefined"!=typeof window,nL={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},nF={};for(let t in nL)nF[t]={isEnabled:e=>nL[t].some(t=>!!e[t])};let nO=Symbol.for("motionComponentSymbol"),nz=nR?re.useLayoutEffect:re.useEffect;function nB(t,{layout:e,layoutId:i}){return x.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!rl[t]||"opacity"===t)}let nI=(t,e)=>e&&"number"==typeof t?e.transform(t):t,nU={...K,transform:Math.round},nN={borderWidth:tu,borderTopWidth:tu,borderRightWidth:tu,borderBottomWidth:tu,borderLeftWidth:tu,borderRadius:tu,radius:tu,borderTopLeftRadius:tu,borderTopRightRadius:tu,borderBottomRightRadius:tu,borderBottomLeftRadius:tu,width:tu,maxWidth:tu,height:tu,maxHeight:tu,top:tu,right:tu,bottom:tu,left:tu,padding:tu,paddingTop:tu,paddingRight:tu,paddingBottom:tu,paddingLeft:tu,margin:tu,marginTop:tu,marginRight:tu,marginBottom:tu,marginLeft:tu,backgroundPositionX:tu,backgroundPositionY:tu,rotate:ta,rotateX:ta,rotateY:ta,rotateZ:ta,scale:G,scaleX:G,scaleY:G,scaleZ:G,skew:ta,skewX:ta,skewY:ta,distance:tu,translateX:tu,translateY:tu,translateZ:tu,x:tu,y:tu,z:tu,perspective:tu,transformPerspective:tu,opacity:X,originX:tc,originY:tc,originZ:tu,zIndex:nU,fillOpacity:X,strokeOpacity:X,numOctaves:nU},n$={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},nW=v.length;function n_(t,e,i){let{style:r,vars:n,transformOrigin:s}=t,o=!1,a=!1;for(let t in e){let i=e[t];if(x.has(t)){o=!0;continue}if(_(t)){n[t]=i;continue}{let e=nI(i,nN[t]);t.startsWith("origin")?(a=!0,s[t]=e):r[t]=e}}if(!e.transform&&(o||i?r.transform=function(t,e,i){let r="",n=!0;for(let s=0;s<nW;s++){let o=v[s],a=t[o];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===+!!o.startsWith("scale"):0===parseFloat(a))||i){let t=nI(a,nN[o]);if(!l){n=!1;let e=n$[o]||o;r+=`${e}(${t}) `}i&&(e[o]=t)}}return r=r.trim(),i?r=i(e,n?"":r):n&&(r="none"),r}(e,t.transform,i):r.transform&&(r.transform="none")),a){let{originX:t="50%",originY:e="50%",originZ:i=0}=s;r.transformOrigin=`${t} ${e} ${i}`}}let nq=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function nY(t,e,i){for(let r in e)D(e[r])||nB(r,i)||(t[r]=e[r])}let nH={offset:"stroke-dashoffset",array:"stroke-dasharray"},nK={offset:"strokeDashoffset",array:"strokeDasharray"};function nX(t,{attrX:e,attrY:i,attrScale:r,pathLength:n,pathSpacing:s=1,pathOffset:o=0,...a},l,u,h){if(n_(t,a,u),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:d,style:c}=t;d.transform&&(c.transform=d.transform,delete d.transform),(c.transform||d.transformOrigin)&&(c.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),c.transform&&(c.transformBox=h?.transformBox??"fill-box",delete d.transformBox),void 0!==e&&(d.x=e),void 0!==i&&(d.y=i),void 0!==r&&(d.scale=r),void 0!==n&&function(t,e,i=1,r=0,n=!0){t.pathLength=1;let s=n?nH:nK;t[s.offset]=tu.transform(-r);let o=tu.transform(e),a=tu.transform(i);t[s.array]=`${o} ${a}`}(d,n,s,o,!1)}let nG=()=>({...nq(),attrs:{}}),nZ=t=>"string"==typeof t&&"svg"===t.toLowerCase(),nQ=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function nJ(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||nQ.has(t)}let n0=t=>!nJ(t);try{!function(t){t&&(n0=e=>e.startsWith("on")?!nJ(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}let n1=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function n2(t){if("string"!=typeof t||t.includes("-"));else if(n1.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}let n5=t=>(e,i)=>{let r=(0,re.useContext)(nV),s=(0,re.useContext)(ri),a=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,r,s){return{latestValues:function(t,e,i,r){let s={},a=r(t,{});for(let t in a)s[t]=rf(a[t]);let{initial:l,animate:u}=t,h=nC(t),d=nD(t);e&&d&&!h&&!1!==t.inherit&&(void 0===l&&(l=e.initial),void 0===u&&(u=e.animate));let c=!!i&&!1===i.initial,p=(c=c||!1===l)?u:l;if(p&&"boolean"!=typeof p&&!n(p)){let e=Array.isArray(p)?p:[p];for(let i=0;i<e.length;i++){let r=o(t,e[i]);if(r){let{transitionEnd:t,transition:e,...i}=r;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=c?e.length-1:0;e=e[t]}null!==e&&(s[t]=e)}for(let e in t)s[e]=t[e]}}}return s}(i,r,s,t),renderState:e()}})(t,e,r,s);return i?a():function(t){let e=(0,re.useRef)(null);return null===e.current&&(e.current=t()),e.current}(a)};function n3(t,e,i){let{style:r}=t,n={};for(let s in r)(D(r[s])||e.style&&D(e.style[s])||nB(s,t)||i?.getValue(s)?.liveStyle!==void 0)&&(n[s]=r[s]);return n}let n9={useVisualState:n5({scrapeMotionValuesFromProps:n3,createRenderState:nq})};function n6(t,e,i){let r=n3(t,e,i);for(let i in t)(D(t[i])||D(e[i]))&&(r[-1!==v.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return r}let n4={useVisualState:n5({scrapeMotionValuesFromProps:n6,createRenderState:nG})},n8=t=>e=>e.test(t),n7=[K,tu,tl,ta,td,th,{test:t=>"auto"===t,parse:t=>t}],st=t=>n7.find(n8(t)),se=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),si=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,sr=t=>/^0[^.\s]+$/u.test(t),sn=new Set(["brightness","contrast","saturate","opacity"]);function ss(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[r]=i.match(Q)||[];if(!r)return t;let n=i.replace(r,""),s=+!!sn.has(e);return r!==i&&(s*=100),e+"("+s+n+")"}let so=/\b([a-z-]*)\(.*?\)/gu,sa={...tA,getAnimatableNone:t=>{let e=t.match(so);return e?e.map(ss).join(" "):t}},sl={...nN,color:tm,backgroundColor:tm,outlineColor:tm,fill:tm,stroke:tm,borderColor:tm,borderTopColor:tm,borderRightColor:tm,borderBottomColor:tm,borderLeftColor:tm,filter:sa,WebkitFilter:sa},su=t=>sl[t];function sh(t,e){let i=su(t);return i!==sa&&(i=tA),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let sd=new Set(["auto","none","0"]);class sc extends eF{constructor(t,e,i,r,n){super(t,e,i,r,n,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let r=t[i];if("string"==typeof r&&Y(r=r.trim())){let n=function t(e,i,r=1){$(r<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[n,s]=function(t){let e=si.exec(t);if(!e)return[,];let[,i,r,n]=e;return[`--${i??r}`,n]}(e);if(!n)return;let o=window.getComputedStyle(i).getPropertyValue(n);if(o){let t=o.trim();return se(t)?parseFloat(t):t}return Y(s)?t(s,i,r+1):s}(r,e.current);void 0!==n&&(t[i]=n),i===t.length-1&&(this.finalKeyframe=r)}}if(this.resolveNoneKeyframes(),!b.has(i)||2!==t.length)return;let[r,n]=t,s=st(r),o=st(n);if(s!==o)if(eP(s)&&eP(o))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else eE[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var r;(null===t[e]||("number"==typeof(r=t[e])?0===r:null===r||"none"===r||"0"===r||sr(r)))&&i.push(e)}i.length&&function(t,e,i){let r,n=0;for(;n<t.length&&!r;){let e=t[n];"string"==typeof e&&!sd.has(e)&&tx(e).values.length&&(r=t[n]),n++}if(r&&i)for(let n of e)t[n]=sh(i,r)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=eE[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let r=e[e.length-1];void 0!==r&&t.getValue(i,r).jump(r,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let r=t.getValue(e);r&&r.jump(this.measuredOrigin,!1);let n=i.length-1,s=i[n];i[n]=eE[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==s&&void 0===this.finalKeyframe&&(this.finalKeyframe=s),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}let sp=[...n7,tm,tA],sm=t=>sp.find(n8(t)),sf={current:null},sy={current:!1},sg=new WeakMap,sv=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class sx{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:r,blockInitialAnimation:n,visualState:s},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=eF,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=P.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,m.render(this.render,!1,!0))};let{latestValues:a,renderState:l}=s;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=r,this.options=o,this.blockInitialAnimation=!!n,this.isControllingVariants=nC(e),this.isVariantNode=nD(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:u,...h}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in h){let e=h[t];void 0!==a[t]&&D(e)&&e.set(a[t],!1)}}mount(t){this.current=t,sg.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),sy.current||function(){if(sy.current=!0,nR)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>sf.current=t.matches;t.addListener(e),e()}else sf.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||sf.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),f(this.notifyUpdate),f(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let r=x.has(t);r&&this.onBindTransform&&this.onBindTransform();let n=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&m.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),s=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{n(),s(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in nF){let e=nF[t];if(!e)continue;let{isEnabled:i,Feature:r}=e;if(!this.features[t]&&r&&i(this.props)&&(this.features[t]=new r(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):iC()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<sv.length;e++){let i=sv[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let r=t["on"+i];r&&(this.propEventSubscriptions[i]=this.on(i,r))}this.prevMotionValues=function(t,e,i){for(let r in e){let n=e[r],s=i[r];if(D(n))t.addValue(r,n);else if(D(s))t.addValue(r,V(n,{owner:t}));else if(s!==n)if(t.hasValue(r)){let e=t.getValue(r);!0===e.liveStyle?e.jump(n):e.hasAnimated||e.set(n)}else{let e=t.getStaticValue(r);t.addValue(r,V(void 0!==e?e:n,{owner:t}))}}for(let r in i)void 0===e[r]&&t.removeValue(r);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=V(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=i&&("string"==typeof i&&(se(i)||sr(i))?i=parseFloat(i):!sm(i)&&tA.test(e)&&(i=sh(t,e)),this.setBaseTarget(t,D(i)?i.get():i)),D(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let r=o(this.props,i,this.presenceContext?.custom);r&&(e=r[t])}if(i&&void 0!==e)return e;let r=this.getBaseTargetFromProps(this.props,t);return void 0===r||D(r)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:r}on(t,e){return this.events[t]||(this.events[t]=new A),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class sb extends sx{constructor(){super(...arguments),this.KeyframeResolver=sc}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;D(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}function sw(t,{style:e,vars:i},r,n){for(let s in Object.assign(t.style,e,n&&n.getProjectionStyles(r)),i)t.style.setProperty(s,i[s])}class sk extends sb{constructor(){super(...arguments),this.type="html",this.renderInstance=sw}readValueFromInstance(t,e){if(x.has(e))return this.projection?.isProjecting?ew(e):eA(t,e);{let i=window.getComputedStyle(t),r=(_(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof r?r.trim():r}}measureInstanceViewportBox(t,{transformPagePoint:e}){return i$(t,e)}build(t,e,i){n_(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return n3(t,e,i)}}let sA=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class sT extends sb{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=iC}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(x.has(e)){let t=su(e);return t&&t.default||0}return e=sA.has(e)?e:R(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return n6(t,e,i)}build(t,e,i){nX(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,r){for(let i in sw(t,e,void 0,r),e.attrs)t.setAttribute(sA.has(i)?i:R(i),e.attrs[i])}mount(t){this.isSVGTag=nZ(t.tagName),super.mount(t)}}let sP=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,r)=>"create"===r?t:(e.has(r)||e.set(r,t(r)),e.get(r))})}((i4={animation:{Feature:ih},exit:{Feature:ic},inView:{Feature:nM},tap:{Feature:nb},focus:{Feature:nd},hover:{Feature:nh},pan:{Feature:i6},drag:{Feature:i3,ProjectionNode:no,MeasureLayout:rh},layout:{ProjectionNode:no,MeasureLayout:rh}},i8=(t,e)=>n2(t)?new sT(e):new sk(e,{allowProjection:t!==re.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function(t){var e,i;let{preloadedFeatures:r,createVisualElement:n,useRender:s,useVisualState:o,Component:a}=t;function l(t,e){var i,r,l;let u,h={...(0,re.useContext)(nE),...t,layoutId:function(t){let{layoutId:e}=t,i=(0,re.useContext)(rr).id;return i&&void 0!==e?i+"-"+e:e}(t)},{isStatic:d}=h,c=function(t){let{initial:e,animate:i}=function(t,e){if(nC(t)){let{initial:e,animate:i}=t;return{initial:!1===e||it(e)?e:void 0,animate:it(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,re.useContext)(nV));return(0,re.useMemo)(()=>({initial:e,animate:i}),[nj(e),nj(i)])}(t),p=o(t,d);if(!d&&nR){r=0,l=0,(0,re.useContext)(nS).strict;let t=function(t){let{drag:e,layout:i}=nF;if(!e&&!i)return{};let r={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(h);u=t.MeasureLayout,c.visualElement=function(t,e,i,r,n){let{visualElement:s}=(0,re.useContext)(nV),o=(0,re.useContext)(nS),a=(0,re.useContext)(ri),l=(0,re.useContext)(nE).reducedMotion,u=(0,re.useRef)(null);r=r||o.renderer,!u.current&&r&&(u.current=r(t,{visualState:e,parent:s,props:i,presenceContext:a,blockInitialAnimation:!!a&&!1===a.initial,reducedMotionConfig:l}));let h=u.current,d=(0,re.useContext)(rn);h&&!h.projection&&n&&("html"===h.type||"svg"===h.type)&&function(t,e,i,r){let{layoutId:n,layout:s,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:h}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:n,layout:s,alwaysMeasureLayout:!!o||a&&i_(a),visualElement:t,animationType:"string"==typeof s?s:"both",initialPromotionConfig:r,crossfade:h,layoutScroll:l,layoutRoot:u})}(u.current,i,n,d);let c=(0,re.useRef)(!1);(0,re.useInsertionEffect)(()=>{h&&c.current&&h.update(i,a)});let p=i[L],m=(0,re.useRef)(!!p&&!window.MotionHandoffIsComplete?.(p)&&window.MotionHasOptimisedAnimation?.(p));return nz(()=>{h&&(c.current=!0,window.MotionIsMounted=!0,h.updateFeatures(),rt.render(h.render),m.current&&h.animationState&&h.animationState.animateChanges())}),(0,re.useEffect)(()=>{h&&(!m.current&&h.animationState&&h.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(p)}),m.current=!1))}),h}(a,p,h,n,t.ProjectionNode)}return(0,i7.jsxs)(nV.Provider,{value:c,children:[u&&c.visualElement?(0,i7.jsx)(u,{visualElement:c.visualElement,...h}):null,s(a,t,(i=c.visualElement,(0,re.useCallback)(t=>{t&&p.onMount&&p.onMount(t),i&&(t?i.mount(t):i.unmount()),e&&("function"==typeof e?e(t):i_(e)&&(e.current=t))},[i])),p,d,c.visualElement)]})}r&&function(t){for(let e in t)nF[e]={...nF[e],...t[e]}}(r),l.displayName="motion.".concat("string"==typeof a?a:"create(".concat(null!=(i=null!=(e=a.displayName)?e:a.name)?i:"",")"));let u=(0,re.forwardRef)(l);return u[nO]=a,u}({...n2(t)?n4:n9,preloadedFeatures:i4,useRender:function(t=!1){return(e,i,r,{latestValues:n},s)=>{let o=(n2(e)?function(t,e,i,r){let n=(0,re.useMemo)(()=>{let i=nG();return nX(i,e,nZ(r),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};nY(e,t.style,t),n.style={...e,...n.style}}return n}:function(t,e){let i={},r=function(t,e){let i=t.style||{},r={};return nY(r,i,t),Object.assign(r,function({transformTemplate:t},e){return(0,re.useMemo)(()=>{let i=nq();return n_(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),r}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=r,i})(i,n,s,e),a=function(t,e,i){let r={};for(let n in t)("values"!==n||"object"!=typeof t.values)&&(n0(n)||!0===i&&nJ(n)||!e&&!nJ(n)||t.draggable&&n.startsWith("onDrag"))&&(r[n]=t[n]);return r}(i,"string"==typeof e,t),l=e!==re.Fragment?{...a,...o,ref:r}:{},{children:u}=i,h=(0,re.useMemo)(()=>D(u)?u.get():u,[u]);return(0,re.createElement)(e,{...l,children:h})}}(e),createVisualElement:i8,Component:t})}))},2138:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(9946).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},2280:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(9946).A)("tablet",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["line",{x1:"12",x2:"12.01",y1:"18",y2:"18",key:"1dp563"}]])},2417:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(9946).A)("panels-top-left",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M9 21V9",key:"1oto5p"}]])},2596:(t,e,i)=>{i.d(e,{$:()=>r});function r(){for(var t,e,i=0,r="",n=arguments.length;i<n;i++)(t=arguments[i])&&(e=function t(e){var i,r,n="";if("string"==typeof e||"number"==typeof e)n+=e;else if("object"==typeof e)if(Array.isArray(e)){var s=e.length;for(i=0;i<s;i++)e[i]&&(r=t(e[i]))&&(n&&(n+=" "),n+=r)}else for(r in e)e[r]&&(n&&(n+=" "),n+=r);return n}(t))&&(r&&(r+=" "),r+=e);return r}},2657:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2664:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"isLocalURL",{enumerable:!0,get:function(){return s}});let r=i(9991),n=i(7102);function s(t){if(!(0,r.isAbsoluteUrl)(t))return!0;try{let e=(0,r.getLocationOrigin)(),i=new URL(t,e);return i.origin===e&&(0,n.hasBasePath)(i.pathname)}catch(t){return!1}}},2713:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(9946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},2757:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{formatUrl:function(){return s},formatWithValidation:function(){return a},urlObjectKeys:function(){return o}});let r=i(6966)._(i(8859)),n=/https?|ftp|gopher|file/;function s(t){let{auth:e,hostname:i}=t,s=t.protocol||"",o=t.pathname||"",a=t.hash||"",l=t.query||"",u=!1;e=e?encodeURIComponent(e).replace(/%3A/i,":")+"@":"",t.host?u=e+t.host:i&&(u=e+(~i.indexOf(":")?"["+i+"]":i),t.port&&(u+=":"+t.port)),l&&"object"==typeof l&&(l=String(r.urlQueryToSearchParams(l)));let h=t.search||l&&"?"+l||"";return s&&!s.endsWith(":")&&(s+=":"),t.slashes||(!s||n.test(s))&&!1!==u?(u="//"+(u||""),o&&"/"!==o[0]&&(o="/"+o)):u||(u=""),a&&"#"!==a[0]&&(a="#"+a),h&&"?"!==h[0]&&(h="?"+h),""+s+u+(o=o.replace(/[?#]/g,encodeURIComponent))+(h=h.replace("#","%23"))+a}let o=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function a(t){return s(t)}},2894:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(9946).A)("linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]])},2919:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(9946).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},3127:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(9946).A)("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]])},3180:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"errorOnce",{enumerable:!0,get:function(){return i}});let i=t=>{}},3311:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(9946).A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},4416:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4516:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(9946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},4616:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},4738:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(9946).A)("monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]])},4783:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(9946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},4869:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(9946).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},5196:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(9946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5487:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(9946).A)("server",[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]])},5525:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(9946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},5684:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(9946).A)("instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]])},5690:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(9946).A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},6474:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(9946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},6517:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(9946).A)("wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]])},6654:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"useMergedRef",{enumerable:!0,get:function(){return n}});let r=i(2115);function n(t,e){let i=(0,r.useRef)(null),n=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let t=i.current;t&&(i.current=null,t());let e=n.current;e&&(n.current=null,e())}else t&&(i.current=s(t,r)),e&&(n.current=s(e,r))},[t,e])}function s(t,e){if("function"!=typeof t)return t.current=e,()=>{t.current=null};{let i=t(e);return"function"==typeof i?i:()=>t(null)}}("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},6767:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(9946).A)("smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]])},6874:(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{default:function(){return y},useLinkStatus:function(){return v}});let r=i(6966),n=i(5155),s=r._(i(2115)),o=i(2757),a=i(5227),l=i(9818),u=i(6654),h=i(9991),d=i(5929);i(3230);let c=i(4930),p=i(2664),m=i(6634);function f(t){return"string"==typeof t?t:(0,o.formatUrl)(t)}function y(t){let e,i,r,[o,y]=(0,s.useOptimistic)(c.IDLE_LINK_STATUS),v=(0,s.useRef)(null),{href:x,as:b,children:w,prefetch:k=null,passHref:A,replace:T,shallow:P,scroll:M,onClick:S,onMouseEnter:E,onTouchStart:V,legacyBehavior:C=!1,onNavigate:D,ref:j,unstable_dynamicOnHover:R,...L}=t;e=w,C&&("string"==typeof e||"number"==typeof e)&&(e=(0,n.jsx)("a",{children:e}));let F=s.default.useContext(a.AppRouterContext),O=!1!==k,z=null===k?l.PrefetchKind.AUTO:l.PrefetchKind.FULL,{href:B,as:I}=s.default.useMemo(()=>{let t=f(x);return{href:t,as:b?f(b):t}},[x,b]);C&&(i=s.default.Children.only(e));let U=C?i&&"object"==typeof i&&i.ref:j,N=s.default.useCallback(t=>(null!==F&&(v.current=(0,c.mountLinkInstance)(t,B,F,z,O,y)),()=>{v.current&&((0,c.unmountLinkForCurrentNavigation)(v.current),v.current=null),(0,c.unmountPrefetchableInstance)(t)}),[O,B,F,z,y]),$={ref:(0,u.useMergedRef)(N,U),onClick(t){C||"function"!=typeof S||S(t),C&&i.props&&"function"==typeof i.props.onClick&&i.props.onClick(t),F&&(t.defaultPrevented||function(t,e,i,r,n,o,a){let{nodeName:l}=t.currentTarget;if(!("A"===l.toUpperCase()&&function(t){let e=t.currentTarget.getAttribute("target");return e&&"_self"!==e||t.metaKey||t.ctrlKey||t.shiftKey||t.altKey||t.nativeEvent&&2===t.nativeEvent.which}(t)||t.currentTarget.hasAttribute("download"))){if(!(0,p.isLocalURL)(e)){n&&(t.preventDefault(),location.replace(e));return}t.preventDefault(),s.default.startTransition(()=>{if(a){let t=!1;if(a({preventDefault:()=>{t=!0}}),t)return}(0,m.dispatchNavigateAction)(i||e,n?"replace":"push",null==o||o,r.current)})}}(t,B,I,v,T,M,D))},onMouseEnter(t){C||"function"!=typeof E||E(t),C&&i.props&&"function"==typeof i.props.onMouseEnter&&i.props.onMouseEnter(t),F&&O&&(0,c.onNavigationIntent)(t.currentTarget,!0===R)},onTouchStart:function(t){C||"function"!=typeof V||V(t),C&&i.props&&"function"==typeof i.props.onTouchStart&&i.props.onTouchStart(t),F&&O&&(0,c.onNavigationIntent)(t.currentTarget,!0===R)}};return(0,h.isAbsoluteUrl)(I)?$.href=I:C&&!A&&("a"!==i.type||"href"in i.props)||($.href=(0,d.addBasePath)(I)),r=C?s.default.cloneElement(i,$):(0,n.jsx)("a",{...L,...$,children:e}),(0,n.jsx)(g.Provider,{value:o,children:r})}i(3180);let g=(0,s.createContext)(c.IDLE_LINK_STATUS),v=()=>(0,s.useContext)(g);("function"==typeof e.default||"object"==typeof e.default&&null!==e.default)&&void 0===e.default.__esModule&&(Object.defineProperty(e.default,"__esModule",{value:!0}),Object.assign(e.default,e),t.exports=e.default)},7580:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},8126:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(9946).A)("chrome",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["line",{x1:"21.17",x2:"12",y1:"8",y2:"8",key:"a0cw5f"}],["line",{x1:"3.95",x2:"8.54",y1:"6.06",y2:"14",key:"1kftof"}],["line",{x1:"10.88",x2:"15.46",y1:"21.94",y2:"14",key:"1ymyh8"}]])},8175:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(9946).A)("twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]])},8564:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(9946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},8859:(t,e)=>{function i(t){let e={};for(let[i,r]of t.entries()){let t=e[i];void 0===t?e[i]=r:Array.isArray(t)?t.push(r):e[i]=[t,r]}return e}function r(t){return"string"==typeof t?t:("number"!=typeof t||isNaN(t))&&"boolean"!=typeof t?"":String(t)}function n(t){let e=new URLSearchParams;for(let[i,n]of Object.entries(t))if(Array.isArray(n))for(let t of n)e.append(i,r(t));else e.set(i,r(n));return e}function s(t){for(var e=arguments.length,i=Array(e>1?e-1:0),r=1;r<e;r++)i[r-1]=arguments[r];for(let e of i){for(let i of e.keys())t.delete(i);for(let[i,r]of e.entries())t.append(i,r)}return t}Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{assign:function(){return s},searchParamsToUrlQuery:function(){return i},urlQueryToSearchParams:function(){return n}})},8883:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},9074:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9420:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(9946).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},9688:(t,e,i)=>{i.d(e,{QP:()=>tu});let r=t=>{let e=a(t),{conflictingClassGroups:i,conflictingClassGroupModifiers:r}=t;return{getClassGroupId:t=>{let i=t.split("-");return""===i[0]&&1!==i.length&&i.shift(),n(i,e)||o(t)},getConflictingClassGroupIds:(t,e)=>{let n=i[t]||[];return e&&r[t]?[...n,...r[t]]:n}}},n=(t,e)=>{if(0===t.length)return e.classGroupId;let i=t[0],r=e.nextPart.get(i),s=r?n(t.slice(1),r):void 0;if(s)return s;if(0===e.validators.length)return;let o=t.join("-");return e.validators.find(({validator:t})=>t(o))?.classGroupId},s=/^\[(.+)\]$/,o=t=>{if(s.test(t)){let e=s.exec(t)[1],i=e?.substring(0,e.indexOf(":"));if(i)return"arbitrary.."+i}},a=t=>{let{theme:e,classGroups:i}=t,r={nextPart:new Map,validators:[]};for(let t in i)l(i[t],r,t,e);return r},l=(t,e,i,r)=>{t.forEach(t=>{if("string"==typeof t){(""===t?e:u(e,t)).classGroupId=i;return}if("function"==typeof t)return h(t)?void l(t(r),e,i,r):void e.validators.push({validator:t,classGroupId:i});Object.entries(t).forEach(([t,n])=>{l(n,u(e,t),i,r)})})},u=(t,e)=>{let i=t;return e.split("-").forEach(t=>{i.nextPart.has(t)||i.nextPart.set(t,{nextPart:new Map,validators:[]}),i=i.nextPart.get(t)}),i},h=t=>t.isThemeGetter,d=t=>{if(t<1)return{get:()=>void 0,set:()=>{}};let e=0,i=new Map,r=new Map,n=(n,s)=>{i.set(n,s),++e>t&&(e=0,r=i,i=new Map)};return{get(t){let e=i.get(t);return void 0!==e?e:void 0!==(e=r.get(t))?(n(t,e),e):void 0},set(t,e){i.has(t)?i.set(t,e):n(t,e)}}},c=t=>{let{prefix:e,experimentalParseClassName:i}=t,r=t=>{let e,i=[],r=0,n=0,s=0;for(let o=0;o<t.length;o++){let a=t[o];if(0===r&&0===n){if(":"===a){i.push(t.slice(s,o)),s=o+1;continue}if("/"===a){e=o;continue}}"["===a?r++:"]"===a?r--:"("===a?n++:")"===a&&n--}let o=0===i.length?t:t.substring(s),a=p(o);return{modifiers:i,hasImportantModifier:a!==o,baseClassName:a,maybePostfixModifierPosition:e&&e>s?e-s:void 0}};if(e){let t=e+":",i=r;r=e=>e.startsWith(t)?i(e.substring(t.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:e,maybePostfixModifierPosition:void 0}}if(i){let t=r;r=e=>i({className:e,parseClassName:t})}return r},p=t=>t.endsWith("!")?t.substring(0,t.length-1):t.startsWith("!")?t.substring(1):t,m=t=>{let e=Object.fromEntries(t.orderSensitiveModifiers.map(t=>[t,!0]));return t=>{if(t.length<=1)return t;let i=[],r=[];return t.forEach(t=>{"["===t[0]||e[t]?(i.push(...r.sort(),t),r=[]):r.push(t)}),i.push(...r.sort()),i}},f=t=>({cache:d(t.cacheSize),parseClassName:c(t),sortModifiers:m(t),...r(t)}),y=/\s+/,g=(t,e)=>{let{parseClassName:i,getClassGroupId:r,getConflictingClassGroupIds:n,sortModifiers:s}=e,o=[],a=t.trim().split(y),l="";for(let t=a.length-1;t>=0;t-=1){let e=a[t],{isExternal:u,modifiers:h,hasImportantModifier:d,baseClassName:c,maybePostfixModifierPosition:p}=i(e);if(u){l=e+(l.length>0?" "+l:l);continue}let m=!!p,f=r(m?c.substring(0,p):c);if(!f){if(!m||!(f=r(c))){l=e+(l.length>0?" "+l:l);continue}m=!1}let y=s(h).join(":"),g=d?y+"!":y,v=g+f;if(o.includes(v))continue;o.push(v);let x=n(f,m);for(let t=0;t<x.length;++t){let e=x[t];o.push(g+e)}l=e+(l.length>0?" "+l:l)}return l};function v(){let t,e,i=0,r="";for(;i<arguments.length;)(t=arguments[i++])&&(e=x(t))&&(r&&(r+=" "),r+=e);return r}let x=t=>{let e;if("string"==typeof t)return t;let i="";for(let r=0;r<t.length;r++)t[r]&&(e=x(t[r]))&&(i&&(i+=" "),i+=e);return i},b=t=>{let e=e=>e[t]||[];return e.isThemeGetter=!0,e},w=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,k=/^\((?:(\w[\w-]*):)?(.+)\)$/i,A=/^\d+\/\d+$/,T=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,P=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,M=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,S=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,E=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,V=t=>A.test(t),C=t=>!!t&&!Number.isNaN(Number(t)),D=t=>!!t&&Number.isInteger(Number(t)),j=t=>t.endsWith("%")&&C(t.slice(0,-1)),R=t=>T.test(t),L=()=>!0,F=t=>P.test(t)&&!M.test(t),O=()=>!1,z=t=>S.test(t),B=t=>E.test(t),I=t=>!N(t)&&!H(t),U=t=>tt(t,tn,O),N=t=>w.test(t),$=t=>tt(t,ts,F),W=t=>tt(t,to,C),_=t=>tt(t,ti,O),q=t=>tt(t,tr,B),Y=t=>tt(t,tl,z),H=t=>k.test(t),K=t=>te(t,ts),X=t=>te(t,ta),G=t=>te(t,ti),Z=t=>te(t,tn),Q=t=>te(t,tr),J=t=>te(t,tl,!0),tt=(t,e,i)=>{let r=w.exec(t);return!!r&&(r[1]?e(r[1]):i(r[2]))},te=(t,e,i=!1)=>{let r=k.exec(t);return!!r&&(r[1]?e(r[1]):i)},ti=t=>"position"===t||"percentage"===t,tr=t=>"image"===t||"url"===t,tn=t=>"length"===t||"size"===t||"bg-size"===t,ts=t=>"length"===t,to=t=>"number"===t,ta=t=>"family-name"===t,tl=t=>"shadow"===t;Symbol.toStringTag;let tu=function(t,...e){let i,r,n,s=function(a){return r=(i=f(e.reduce((t,e)=>e(t),t()))).cache.get,n=i.cache.set,s=o,o(a)};function o(t){let e=r(t);if(e)return e;let s=g(t,i);return n(t,s),s}return function(){return s(v.apply(null,arguments))}}(()=>{let t=b("color"),e=b("font"),i=b("text"),r=b("font-weight"),n=b("tracking"),s=b("leading"),o=b("breakpoint"),a=b("container"),l=b("spacing"),u=b("radius"),h=b("shadow"),d=b("inset-shadow"),c=b("text-shadow"),p=b("drop-shadow"),m=b("blur"),f=b("perspective"),y=b("aspect"),g=b("ease"),v=b("animate"),x=()=>["auto","avoid","all","avoid-page","page","left","right","column"],w=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],k=()=>[...w(),H,N],A=()=>["auto","hidden","clip","visible","scroll"],T=()=>["auto","contain","none"],P=()=>[H,N,l],M=()=>[V,"full","auto",...P()],S=()=>[D,"none","subgrid",H,N],E=()=>["auto",{span:["full",D,H,N]},D,H,N],F=()=>[D,"auto",H,N],O=()=>["auto","min","max","fr",H,N],z=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],B=()=>["start","end","center","stretch","center-safe","end-safe"],tt=()=>["auto",...P()],te=()=>[V,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...P()],ti=()=>[t,H,N],tr=()=>[...w(),G,_,{position:[H,N]}],tn=()=>["no-repeat",{repeat:["","x","y","space","round"]}],ts=()=>["auto","cover","contain",Z,U,{size:[H,N]}],to=()=>[j,K,$],ta=()=>["","none","full",u,H,N],tl=()=>["",C,K,$],tu=()=>["solid","dashed","dotted","double"],th=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],td=()=>[C,j,G,_],tc=()=>["","none",m,H,N],tp=()=>["none",C,H,N],tm=()=>["none",C,H,N],tf=()=>[C,H,N],ty=()=>[V,"full",...P()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[R],breakpoint:[R],color:[L],container:[R],"drop-shadow":[R],ease:["in","out","in-out"],font:[I],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[R],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[R],shadow:[R],spacing:["px",C],text:[R],"text-shadow":[R],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",V,N,H,y]}],container:["container"],columns:[{columns:[C,N,H,a]}],"break-after":[{"break-after":x()}],"break-before":[{"break-before":x()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:k()}],overflow:[{overflow:A()}],"overflow-x":[{"overflow-x":A()}],"overflow-y":[{"overflow-y":A()}],overscroll:[{overscroll:T()}],"overscroll-x":[{"overscroll-x":T()}],"overscroll-y":[{"overscroll-y":T()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:M()}],"inset-x":[{"inset-x":M()}],"inset-y":[{"inset-y":M()}],start:[{start:M()}],end:[{end:M()}],top:[{top:M()}],right:[{right:M()}],bottom:[{bottom:M()}],left:[{left:M()}],visibility:["visible","invisible","collapse"],z:[{z:[D,"auto",H,N]}],basis:[{basis:[V,"full","auto",a,...P()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[C,V,"auto","initial","none",N]}],grow:[{grow:["",C,H,N]}],shrink:[{shrink:["",C,H,N]}],order:[{order:[D,"first","last","none",H,N]}],"grid-cols":[{"grid-cols":S()}],"col-start-end":[{col:E()}],"col-start":[{"col-start":F()}],"col-end":[{"col-end":F()}],"grid-rows":[{"grid-rows":S()}],"row-start-end":[{row:E()}],"row-start":[{"row-start":F()}],"row-end":[{"row-end":F()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":O()}],"auto-rows":[{"auto-rows":O()}],gap:[{gap:P()}],"gap-x":[{"gap-x":P()}],"gap-y":[{"gap-y":P()}],"justify-content":[{justify:[...z(),"normal"]}],"justify-items":[{"justify-items":[...B(),"normal"]}],"justify-self":[{"justify-self":["auto",...B()]}],"align-content":[{content:["normal",...z()]}],"align-items":[{items:[...B(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...B(),{baseline:["","last"]}]}],"place-content":[{"place-content":z()}],"place-items":[{"place-items":[...B(),"baseline"]}],"place-self":[{"place-self":["auto",...B()]}],p:[{p:P()}],px:[{px:P()}],py:[{py:P()}],ps:[{ps:P()}],pe:[{pe:P()}],pt:[{pt:P()}],pr:[{pr:P()}],pb:[{pb:P()}],pl:[{pl:P()}],m:[{m:tt()}],mx:[{mx:tt()}],my:[{my:tt()}],ms:[{ms:tt()}],me:[{me:tt()}],mt:[{mt:tt()}],mr:[{mr:tt()}],mb:[{mb:tt()}],ml:[{ml:tt()}],"space-x":[{"space-x":P()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":P()}],"space-y-reverse":["space-y-reverse"],size:[{size:te()}],w:[{w:[a,"screen",...te()]}],"min-w":[{"min-w":[a,"screen","none",...te()]}],"max-w":[{"max-w":[a,"screen","none","prose",{screen:[o]},...te()]}],h:[{h:["screen","lh",...te()]}],"min-h":[{"min-h":["screen","lh","none",...te()]}],"max-h":[{"max-h":["screen","lh",...te()]}],"font-size":[{text:["base",i,K,$]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,H,W]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",j,N]}],"font-family":[{font:[X,N,e]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[n,H,N]}],"line-clamp":[{"line-clamp":[C,"none",H,W]}],leading:[{leading:[s,...P()]}],"list-image":[{"list-image":["none",H,N]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",H,N]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:ti()}],"text-color":[{text:ti()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...tu(),"wavy"]}],"text-decoration-thickness":[{decoration:[C,"from-font","auto",H,$]}],"text-decoration-color":[{decoration:ti()}],"underline-offset":[{"underline-offset":[C,"auto",H,N]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:P()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",H,N]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",H,N]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:tr()}],"bg-repeat":[{bg:tn()}],"bg-size":[{bg:ts()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},D,H,N],radial:["",H,N],conic:[D,H,N]},Q,q]}],"bg-color":[{bg:ti()}],"gradient-from-pos":[{from:to()}],"gradient-via-pos":[{via:to()}],"gradient-to-pos":[{to:to()}],"gradient-from":[{from:ti()}],"gradient-via":[{via:ti()}],"gradient-to":[{to:ti()}],rounded:[{rounded:ta()}],"rounded-s":[{"rounded-s":ta()}],"rounded-e":[{"rounded-e":ta()}],"rounded-t":[{"rounded-t":ta()}],"rounded-r":[{"rounded-r":ta()}],"rounded-b":[{"rounded-b":ta()}],"rounded-l":[{"rounded-l":ta()}],"rounded-ss":[{"rounded-ss":ta()}],"rounded-se":[{"rounded-se":ta()}],"rounded-ee":[{"rounded-ee":ta()}],"rounded-es":[{"rounded-es":ta()}],"rounded-tl":[{"rounded-tl":ta()}],"rounded-tr":[{"rounded-tr":ta()}],"rounded-br":[{"rounded-br":ta()}],"rounded-bl":[{"rounded-bl":ta()}],"border-w":[{border:tl()}],"border-w-x":[{"border-x":tl()}],"border-w-y":[{"border-y":tl()}],"border-w-s":[{"border-s":tl()}],"border-w-e":[{"border-e":tl()}],"border-w-t":[{"border-t":tl()}],"border-w-r":[{"border-r":tl()}],"border-w-b":[{"border-b":tl()}],"border-w-l":[{"border-l":tl()}],"divide-x":[{"divide-x":tl()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":tl()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...tu(),"hidden","none"]}],"divide-style":[{divide:[...tu(),"hidden","none"]}],"border-color":[{border:ti()}],"border-color-x":[{"border-x":ti()}],"border-color-y":[{"border-y":ti()}],"border-color-s":[{"border-s":ti()}],"border-color-e":[{"border-e":ti()}],"border-color-t":[{"border-t":ti()}],"border-color-r":[{"border-r":ti()}],"border-color-b":[{"border-b":ti()}],"border-color-l":[{"border-l":ti()}],"divide-color":[{divide:ti()}],"outline-style":[{outline:[...tu(),"none","hidden"]}],"outline-offset":[{"outline-offset":[C,H,N]}],"outline-w":[{outline:["",C,K,$]}],"outline-color":[{outline:ti()}],shadow:[{shadow:["","none",h,J,Y]}],"shadow-color":[{shadow:ti()}],"inset-shadow":[{"inset-shadow":["none",d,J,Y]}],"inset-shadow-color":[{"inset-shadow":ti()}],"ring-w":[{ring:tl()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:ti()}],"ring-offset-w":[{"ring-offset":[C,$]}],"ring-offset-color":[{"ring-offset":ti()}],"inset-ring-w":[{"inset-ring":tl()}],"inset-ring-color":[{"inset-ring":ti()}],"text-shadow":[{"text-shadow":["none",c,J,Y]}],"text-shadow-color":[{"text-shadow":ti()}],opacity:[{opacity:[C,H,N]}],"mix-blend":[{"mix-blend":[...th(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":th()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[C]}],"mask-image-linear-from-pos":[{"mask-linear-from":td()}],"mask-image-linear-to-pos":[{"mask-linear-to":td()}],"mask-image-linear-from-color":[{"mask-linear-from":ti()}],"mask-image-linear-to-color":[{"mask-linear-to":ti()}],"mask-image-t-from-pos":[{"mask-t-from":td()}],"mask-image-t-to-pos":[{"mask-t-to":td()}],"mask-image-t-from-color":[{"mask-t-from":ti()}],"mask-image-t-to-color":[{"mask-t-to":ti()}],"mask-image-r-from-pos":[{"mask-r-from":td()}],"mask-image-r-to-pos":[{"mask-r-to":td()}],"mask-image-r-from-color":[{"mask-r-from":ti()}],"mask-image-r-to-color":[{"mask-r-to":ti()}],"mask-image-b-from-pos":[{"mask-b-from":td()}],"mask-image-b-to-pos":[{"mask-b-to":td()}],"mask-image-b-from-color":[{"mask-b-from":ti()}],"mask-image-b-to-color":[{"mask-b-to":ti()}],"mask-image-l-from-pos":[{"mask-l-from":td()}],"mask-image-l-to-pos":[{"mask-l-to":td()}],"mask-image-l-from-color":[{"mask-l-from":ti()}],"mask-image-l-to-color":[{"mask-l-to":ti()}],"mask-image-x-from-pos":[{"mask-x-from":td()}],"mask-image-x-to-pos":[{"mask-x-to":td()}],"mask-image-x-from-color":[{"mask-x-from":ti()}],"mask-image-x-to-color":[{"mask-x-to":ti()}],"mask-image-y-from-pos":[{"mask-y-from":td()}],"mask-image-y-to-pos":[{"mask-y-to":td()}],"mask-image-y-from-color":[{"mask-y-from":ti()}],"mask-image-y-to-color":[{"mask-y-to":ti()}],"mask-image-radial":[{"mask-radial":[H,N]}],"mask-image-radial-from-pos":[{"mask-radial-from":td()}],"mask-image-radial-to-pos":[{"mask-radial-to":td()}],"mask-image-radial-from-color":[{"mask-radial-from":ti()}],"mask-image-radial-to-color":[{"mask-radial-to":ti()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":w()}],"mask-image-conic-pos":[{"mask-conic":[C]}],"mask-image-conic-from-pos":[{"mask-conic-from":td()}],"mask-image-conic-to-pos":[{"mask-conic-to":td()}],"mask-image-conic-from-color":[{"mask-conic-from":ti()}],"mask-image-conic-to-color":[{"mask-conic-to":ti()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:tr()}],"mask-repeat":[{mask:tn()}],"mask-size":[{mask:ts()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",H,N]}],filter:[{filter:["","none",H,N]}],blur:[{blur:tc()}],brightness:[{brightness:[C,H,N]}],contrast:[{contrast:[C,H,N]}],"drop-shadow":[{"drop-shadow":["","none",p,J,Y]}],"drop-shadow-color":[{"drop-shadow":ti()}],grayscale:[{grayscale:["",C,H,N]}],"hue-rotate":[{"hue-rotate":[C,H,N]}],invert:[{invert:["",C,H,N]}],saturate:[{saturate:[C,H,N]}],sepia:[{sepia:["",C,H,N]}],"backdrop-filter":[{"backdrop-filter":["","none",H,N]}],"backdrop-blur":[{"backdrop-blur":tc()}],"backdrop-brightness":[{"backdrop-brightness":[C,H,N]}],"backdrop-contrast":[{"backdrop-contrast":[C,H,N]}],"backdrop-grayscale":[{"backdrop-grayscale":["",C,H,N]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[C,H,N]}],"backdrop-invert":[{"backdrop-invert":["",C,H,N]}],"backdrop-opacity":[{"backdrop-opacity":[C,H,N]}],"backdrop-saturate":[{"backdrop-saturate":[C,H,N]}],"backdrop-sepia":[{"backdrop-sepia":["",C,H,N]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":P()}],"border-spacing-x":[{"border-spacing-x":P()}],"border-spacing-y":[{"border-spacing-y":P()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",H,N]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[C,"initial",H,N]}],ease:[{ease:["linear","initial",g,H,N]}],delay:[{delay:[C,H,N]}],animate:[{animate:["none",v,H,N]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[f,H,N]}],"perspective-origin":[{"perspective-origin":k()}],rotate:[{rotate:tp()}],"rotate-x":[{"rotate-x":tp()}],"rotate-y":[{"rotate-y":tp()}],"rotate-z":[{"rotate-z":tp()}],scale:[{scale:tm()}],"scale-x":[{"scale-x":tm()}],"scale-y":[{"scale-y":tm()}],"scale-z":[{"scale-z":tm()}],"scale-3d":["scale-3d"],skew:[{skew:tf()}],"skew-x":[{"skew-x":tf()}],"skew-y":[{"skew-y":tf()}],transform:[{transform:[H,N,"","none","gpu","cpu"]}],"transform-origin":[{origin:k()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:ty()}],"translate-x":[{"translate-x":ty()}],"translate-y":[{"translate-y":ty()}],"translate-z":[{"translate-z":ty()}],"translate-none":["translate-none"],accent:[{accent:ti()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:ti()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",H,N]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":P()}],"scroll-mx":[{"scroll-mx":P()}],"scroll-my":[{"scroll-my":P()}],"scroll-ms":[{"scroll-ms":P()}],"scroll-me":[{"scroll-me":P()}],"scroll-mt":[{"scroll-mt":P()}],"scroll-mr":[{"scroll-mr":P()}],"scroll-mb":[{"scroll-mb":P()}],"scroll-ml":[{"scroll-ml":P()}],"scroll-p":[{"scroll-p":P()}],"scroll-px":[{"scroll-px":P()}],"scroll-py":[{"scroll-py":P()}],"scroll-ps":[{"scroll-ps":P()}],"scroll-pe":[{"scroll-pe":P()}],"scroll-pt":[{"scroll-pt":P()}],"scroll-pr":[{"scroll-pr":P()}],"scroll-pb":[{"scroll-pb":P()}],"scroll-pl":[{"scroll-pl":P()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",H,N]}],fill:[{fill:["none",...ti()]}],"stroke-w":[{stroke:[C,K,$,W]}],stroke:[{stroke:["none",...ti()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},9803:(t,e,i)=>{i.d(e,{A:()=>r});let r=(0,i(9946).A)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]])},9946:(t,e,i)=>{i.d(e,{A:()=>d});var r=i(2115);let n=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=t=>t.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,e,i)=>i?i.toUpperCase():e.toLowerCase()),o=t=>{let e=s(t);return e.charAt(0).toUpperCase()+e.slice(1)},a=function(){for(var t=arguments.length,e=Array(t),i=0;i<t;i++)e[i]=arguments[i];return e.filter((t,e,i)=>!!t&&""!==t.trim()&&i.indexOf(t)===e).join(" ").trim()},l=t=>{for(let e in t)if(e.startsWith("aria-")||"role"===e||"title"===e)return!0};var u={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let h=(0,r.forwardRef)((t,e)=>{let{color:i="currentColor",size:n=24,strokeWidth:s=2,absoluteStrokeWidth:o,className:h="",children:d,iconNode:c,...p}=t;return(0,r.createElement)("svg",{ref:e,...u,width:n,height:n,stroke:i,strokeWidth:o?24*Number(s)/Number(n):s,className:a("lucide",h),...!d&&!l(p)&&{"aria-hidden":"true"},...p},[...c.map(t=>{let[e,i]=t;return(0,r.createElement)(e,i)}),...Array.isArray(d)?d:[d]])}),d=(t,e)=>{let i=(0,r.forwardRef)((i,s)=>{let{className:l,...u}=i;return(0,r.createElement)(h,{ref:s,iconNode:e,className:a("lucide-".concat(n(o(t))),"lucide-".concat(t),l),...u})});return i.displayName=o(t),i}},9991:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),!function(t,e){for(var i in e)Object.defineProperty(t,i,{enumerable:!0,get:e[i]})}(e,{DecodeError:function(){return m},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return g},NormalizeError:function(){return f},PageNotFoundError:function(){return y},SP:function(){return c},ST:function(){return p},WEB_VITALS:function(){return i},execOnce:function(){return r},getDisplayName:function(){return l},getLocationOrigin:function(){return o},getURL:function(){return a},isAbsoluteUrl:function(){return s},isResSent:function(){return u},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return h},stringifyError:function(){return x}});let i=["CLS","FCP","FID","INP","LCP","TTFB"];function r(t){let e,i=!1;return function(){for(var r=arguments.length,n=Array(r),s=0;s<r;s++)n[s]=arguments[s];return i||(i=!0,e=t(...n)),e}}let n=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,s=t=>n.test(t);function o(){let{protocol:t,hostname:e,port:i}=window.location;return t+"//"+e+(i?":"+i:"")}function a(){let{href:t}=window.location,e=o();return t.substring(e.length)}function l(t){return"string"==typeof t?t:t.displayName||t.name||"Unknown"}function u(t){return t.finished||t.headersSent}function h(t){let e=t.split("?");return e[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(e[1]?"?"+e.slice(1).join("?"):"")}async function d(t,e){let i=e.res||e.ctx&&e.ctx.res;if(!t.getInitialProps)return e.ctx&&e.Component?{pageProps:await d(e.Component,e.ctx)}:{};let r=await t.getInitialProps(e);if(i&&u(i))return r;if(!r)throw Object.defineProperty(Error('"'+l(t)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r}let c="undefined"!=typeof performance,p=c&&["mark","measure","getEntriesByName"].every(t=>"function"==typeof performance[t]);class m extends Error{}class f extends Error{}class y extends Error{constructor(t){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+t}}class g extends Error{constructor(t,e){super(),this.message="Failed to load static file for page: "+t+" "+e}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function x(t){return JSON.stringify({message:t.message,stack:t.stack})}}}]);