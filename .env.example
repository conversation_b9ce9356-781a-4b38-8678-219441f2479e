# Environment variables for Whitespace SaaS

# Site Configuration
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NEXT_PUBLIC_SITE_NAME="Whitespace"
NEXT_PUBLIC_SITE_DESCRIPTION="Project management software that enables your teams to collaborate, plan, analyze and manage everyday tasks"

# Analytics (optional)
# NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX
# NEXT_PUBLIC_HOTJAR_ID=XXXXXXX

# API Configuration (if needed)
# API_BASE_URL=https://api.whitespace.com
# API_KEY=your_api_key_here

# Email Configuration (for contact forms)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASS=your_app_password

# Database (if needed)
# DATABASE_URL=postgresql://username:password@localhost:5432/whitespace

# Authentication (if implementing auth)
# NEXTAUTH_URL=http://localhost:3000
# NEXTAUTH_SECRET=your_secret_here
# GITHUB_ID=your_github_oauth_id
# GITHUB_SECRET=your_github_oauth_secret
